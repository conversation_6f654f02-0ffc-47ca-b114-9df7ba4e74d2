---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
- apiGroups:
  - ""
  resources:
  - pods
  - services
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  - replicasets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - eventing.broadcast.io
  resources:
  - broadcasttriggers
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - eventing.broadcast.io
  resources:
  - broadcasttriggers/finalizers
  verbs:
  - update
- apiGroups:
  - eventing.broadcast.io
  resources:
  - broadcasttriggers/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - eventing.knative.dev
  resources:
  - brokers
  - triggers
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
