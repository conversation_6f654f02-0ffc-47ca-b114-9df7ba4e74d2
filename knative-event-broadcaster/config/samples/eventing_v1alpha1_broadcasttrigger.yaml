apiVersion: eventing.broadcast.io/v1alpha1
kind: BroadcastTrigger
metadata:
  labels:
    app.kubernetes.io/name: knative-event-broadcaster
    app.kubernetes.io/managed-by: kustomize
  name: broadcasttrigger-sample
spec:
  # Knative broker to subscribe to
  broker: default
  
  # Event filter
  filter:
    attributes:
      type: com.example.user.created
      source: user-service
  
  # Target pods
  target:
    selector:
      matchLabels:
        app: notification-service
    port: 8080
    path: /events
  
  # Delivery configuration
  delivery:
    retry: 3
    timeout: 30s
    backoffPolicy: exponential
    backoffDelay: 1s
    ordering: none
  
  # Rate limiting
  rateLimit:
    requestsPerSecond: 100
    burst: 200
