---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
  name: broadcasttriggers.eventing.broadcast.io
spec:
  group: eventing.broadcast.io
  names:
    kind: BroadcastTrigger
    listKind: BroadcastTriggerList
    plural: broadcasttriggers
    singular: broadcasttrigger
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.broker
      name: Broker
      type: string
    - jsonPath: .status.targetPods[*]
      name: Target Pods
      type: integer
    - jsonPath: .status.eventsDelivered
      name: Events Delivered
      type: integer
    - jsonPath: .status.eventsFailed
      name: Events Failed
      type: integer
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: BroadcastTrigger is the Schema for the broadcasttriggers API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: BroadcastTriggerSpec defines the desired state of BroadcastTrigger
            properties:
              broker:
                description: Broker is the name of the Knative broker to subscribe
                  to
                type: string
              delivery:
                description: Delivery defines delivery configuration for event broadcasting
                properties:
                  backoffDelay:
                    default: 1s
                    description: BackoffDelay is the initial delay for backoff
                    type: string
                  backoffPolicy:
                    default: exponential
                    description: BackoffPolicy defines the backoff policy for retries
                    enum:
                    - linear
                    - exponential
                    type: string
                  deadLetterSink:
                    description: DeadLetterSink is the destination for events that
                      fail delivery
                    properties:
                      CACerts:
                        description: |-
                          CACerts are Certification Authority (CA) certificates in PEM format
                          according to https://www.rfc-editor.org/rfc/rfc7468.
                          If set, these CAs are appended to the set of CAs provided
                          by the Addressable target, if any.
                        type: string
                      audience:
                        description: |-
                          Audience is the OIDC audience.
                          This need only be set, if the target is not an Addressable
                          and thus the Audience can't be received from the Addressable itself.
                          In case the Addressable specifies an Audience too, the Destinations
                          Audience takes preference.
                        type: string
                      ref:
                        description: Ref points to an Addressable.
                        properties:
                          address:
                            description: Address points to a specific Address Name.
                            type: string
                          apiVersion:
                            description: API version of the referent.
                            type: string
                          group:
                            description: |-
                              Group of the API, without the version of the group. This can be used as an alternative to the APIVersion, and then resolved using ResolveGroup.
                              Note: This API is EXPERIMENTAL and might break anytime. For more details: https://github.com/knative/eventing/issues/5086
                            type: string
                          kind:
                            description: |-
                              Kind of the referent.
                              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                            type: string
                          name:
                            description: |-
                              Name of the referent.
                              More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                            type: string
                          namespace:
                            description: |-
                              Namespace of the referent.
                              More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                              This is optional field, it gets defaulted to the object holding it if left out.
                            type: string
                        required:
                        - kind
                        - name
                        type: object
                      uri:
                        description: URI can be an absolute URL(non-empty scheme and
                          non-empty host) pointing to the target or a relative URI.
                          Relative URIs will be resolved using the base URI retrieved
                          from Ref.
                        type: string
                    type: object
                  ordering:
                    default: none
                    description: Ordering defines event ordering guarantees
                    enum:
                    - none
                    - strict
                    type: string
                  retry:
                    default: 3
                    description: Retry is the number of retry attempts for failed
                      deliveries
                    format: int32
                    minimum: 0
                    type: integer
                  timeout:
                    default: 30s
                    description: Timeout is the timeout for individual event deliveries
                    type: string
                type: object
              filter:
                description: Filter defines the event filtering criteria
                properties:
                  attributes:
                    additionalProperties:
                      type: string
                    description: Attributes filters events by their attributes
                    type: object
                  cel:
                    description: CEL expression for advanced filtering
                    type: string
                type: object
              rateLimit:
                description: RateLimit defines rate limiting configuration
                properties:
                  burst:
                    default: 200
                    description: Burst is the maximum burst size
                    format: int32
                    minimum: 1
                    type: integer
                  requestsPerSecond:
                    default: 100
                    description: RequestsPerSecond is the maximum number of requests
                      per second
                    format: int32
                    minimum: 1
                    type: integer
                type: object
              target:
                description: Target defines the pods to broadcast events to
                properties:
                  namespace:
                    description: |-
                      Namespace is the namespace to search for pods
                      If not specified, uses the same namespace as the BroadcastTrigger
                    type: string
                  path:
                    default: /
                    description: Path is the HTTP path to send events to
                    type: string
                  port:
                    default: 8080
                    description: Port is the port to send events to on target pods
                    format: int32
                    maximum: 65535
                    minimum: 1
                    type: integer
                  selector:
                    description: Selector is a label selector for target pods
                    properties:
                      matchExpressions:
                        description: matchExpressions is a list of label selector
                          requirements. The requirements are ANDed.
                        items:
                          description: |-
                            A label selector requirement is a selector that contains values, a key, and an operator that
                            relates the key and values.
                          properties:
                            key:
                              description: key is the label key that the selector
                                applies to.
                              type: string
                            operator:
                              description: |-
                                operator represents a key's relationship to a set of values.
                                Valid operators are In, NotIn, Exists and DoesNotExist.
                              type: string
                            values:
                              description: |-
                                values is an array of string values. If the operator is In or NotIn,
                                the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                the values array must be empty. This array is replaced during a strategic
                                merge patch.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          required:
                          - key
                          - operator
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      matchLabels:
                        additionalProperties:
                          type: string
                        description: |-
                          matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                          map is equivalent to an element of matchExpressions, whose key field is "key", the
                          operator is "In", and the values array contains only "value". The requirements are ANDed.
                        type: object
                    type: object
                    x-kubernetes-map-type: atomic
                required:
                - selector
                type: object
            required:
            - broker
            - target
            type: object
          status:
            description: BroadcastTriggerStatus defines the observed state of BroadcastTrigger
            properties:
              conditions:
                description: Conditions represent the latest available observations
                  of the BroadcastTrigger's state
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              eventsDelivered:
                description: EventsDelivered is the total number of events successfully
                  delivered
                format: int64
                type: integer
              eventsFailed:
                description: EventsFailed is the total number of events that failed
                  delivery
                format: int64
                type: integer
              lastEventTime:
                description: LastEventTime is the timestamp of the last event processed
                format: date-time
                type: string
              observedGeneration:
                description: ObservedGeneration is the generation most recently observed
                  by the controller
                format: int64
                type: integer
              targetPods:
                description: TargetPods is the list of currently targeted pods
                items:
                  description: PodReference represents a reference to a target pod
                  properties:
                    ip:
                      description: IP is the pod IP address
                      type: string
                    name:
                      description: Name is the pod name
                      type: string
                    namespace:
                      description: Namespace is the pod namespace
                      type: string
                    ready:
                      description: Ready indicates if the pod is ready to receive
                        events
                      type: boolean
                  required:
                  - ip
                  - name
                  - namespace
                  - ready
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
