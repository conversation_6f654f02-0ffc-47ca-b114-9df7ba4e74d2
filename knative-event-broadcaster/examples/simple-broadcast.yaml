# Example: Simple event broadcasting to all pods of a deployment
apiVersion: eventing.broadcast.io/v1alpha1
kind: BroadcastTrigger
metadata:
  name: simple-broadcast
  namespace: default
spec:
  # Source broker to subscribe to
  broker: default
  
  # Event filter (same syntax as Knative Trigger)
  filter:
    attributes:
      type: com.example.user.created
      source: user-service
  
  # Target pods to broadcast to
  target:
    selector:
      matchLabels:
        app: notification-service
    namespace: default
  
  # Delivery configuration
  delivery:
    retry: 3
    timeout: 30s
---
# Example target deployment that will receive broadcasted events
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-service
  namespace: default
spec:
  replicas: 3
  selector:
    matchLabels:
      app: notification-service
  template:
    metadata:
      labels:
        app: notification-service
    spec:
      containers:
      - name: notification
        image: notification-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          value: "8080"
---
# Service for the deployment (optional, for regular traffic)
apiVersion: v1
kind: Service
metadata:
  name: notification-service
  namespace: default
spec:
  selector:
    app: notification-service
  ports:
  - port: 80
    targetPort: 8080
