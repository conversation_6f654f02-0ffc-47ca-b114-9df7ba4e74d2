# Example: Advanced broadcasting with dead letter handling and multiple filters
apiVersion: eventing.broadcast.io/v1alpha1
kind: BroadcastTrigger
metadata:
  name: advanced-broadcast
  namespace: default
spec:
  # Source broker
  broker: default
  
  # Multiple event filters
  filter:
    attributes:
      type: com.example.order.processed
      source: order-service
    # Additional filtering can be done with CEL expressions
    # cel: 'data.amount > 100'
  
  # Target configuration
  target:
    selector:
      matchLabels:
        app: analytics-service
        tier: processing
    namespace: analytics
  
  # Advanced delivery configuration
  delivery:
    retry: 5
    timeout: 60s
    backoffPolicy: exponential
    backoffDelay: 1s
    
    # Dead letter sink for failed deliveries
    deadLetterSink:
      ref:
        apiVersion: v1
        kind: Service
        name: dead-letter-service
        namespace: default
      
    # Delivery guarantees
    ordering: strict  # or 'none' for parallel delivery
    
  # Rate limiting
  rateLimit:
    requestsPerSecond: 100
    burst: 200
---
# Dead letter service to handle failed events
apiVersion: v1
kind: Service
metadata:
  name: dead-letter-service
  namespace: default
spec:
  selector:
    app: dead-letter-handler
  ports:
  - port: 80
    targetPort: 8080
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dead-letter-handler
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dead-letter-handler
  template:
    metadata:
      labels:
        app: dead-letter-handler
    spec:
      containers:
      - name: handler
        image: dead-letter-handler:latest
        ports:
        - containerPort: 8080
