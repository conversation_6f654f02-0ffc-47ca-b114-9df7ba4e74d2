# Knative Event Broadcaster

A Kubernetes controller that extends Knative eventing to broadcast events to all pod instances instead of load-balancing to a single pod.

## Problem Statement

In standard Knative eventing 1.10, when you create a trigger with a broker and service as subscriber, events are delivered to the service which then load-balances to a single pod instance. This project solves the need to send the same event to **all pod instances** simultaneously.

## Architecture

The broadcaster consists of:

- **Broadcasting Controller**: Main orchestrator managing the broadcasting lifecycle
- **Broadcast Trigger CRD**: Custom resource for configuring broadcasting behavior  
- **Pod Discovery Service**: Discovers and tracks target pod instances
- **Event Distributor**: Handles actual event delivery to pods

## Features

- ✅ Direct pod-to-pod event delivery
- ✅ Dynamic pod discovery and lifecycle handling
- ✅ Configurable retry policies and dead letter handling
- ✅ Integration with existing Knative brokers and triggers
- ✅ Support for event filtering and routing
- ✅ Horizontal scaling and high availability

## Quick Start

### Prerequisites

- Kubernetes cluster with Knative eventing 1.10+
- kubectl configured to access your cluster

### Installation

```bash
# Install CRDs
kubectl apply -f config/crd/

# Install RBAC
kubectl apply -f config/rbac/

# Install controller
kubectl apply -f config/manager/
```

### Usage

1. Create a BroadcastTrigger:

```yaml
apiVersion: eventing.broadcast.io/v1alpha1
kind: BroadcastTrigger
metadata:
  name: my-broadcast
  namespace: default
spec:
  broker: default
  filter:
    attributes:
      type: com.example.myevent
  target:
    selector:
      matchLabels:
        app: my-application
    namespace: default
  delivery:
    retry: 3
    timeout: 30s
```

2. Send events to your Knative broker as usual - they will be automatically broadcasted to all matching pods.

## Development

### Prerequisites

- Go 1.24+
- Docker
- kubectl
- kustomize

### Building

```bash
# Build the controller
make build

# Build and push container image
make docker-build docker-push IMG=your-registry/knative-event-broadcaster:latest

# Deploy to cluster
make deploy IMG=your-registry/knative-event-broadcaster:latest
```

### Testing

```bash
# Run unit tests
make test

# Run integration tests
make test-integration
```

## Configuration

### BroadcastTrigger Spec

| Field | Type | Description |
|-------|------|-------------|
| `broker` | string | Name of the Knative broker to subscribe to |
| `filter` | object | Event filtering criteria (same as Knative Trigger) |
| `target.selector` | object | Label selector for target pods |
| `target.namespace` | string | Namespace to search for pods (optional, defaults to trigger namespace) |
| `delivery.retry` | int | Number of retry attempts for failed deliveries |
| `delivery.timeout` | duration | Timeout for individual event deliveries |
| `delivery.deadLetterSink` | object | Destination for failed events |

## Examples

See the [examples](./examples/) directory for complete usage examples.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](../LICENSE) file for details.
