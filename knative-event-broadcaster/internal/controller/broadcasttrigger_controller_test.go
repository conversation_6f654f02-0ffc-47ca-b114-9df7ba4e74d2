/*
Copyright 2025 Alauda DevOps.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	eventingv1alpha1 "github.com/AlaudaDevops/experimental/knative-event-broadcaster/api/v1alpha1"
	"github.com/AlaudaDevops/experimental/knative-event-broadcaster/internal/broadcaster"
)

var _ = Describe("BroadcastTrigger Controller", func() {
	Context("When reconciling a resource", func() {
		const resourceName = "test-resource"

		ctx := context.Background()

		typeNamespacedName := types.NamespacedName{
			Name:      resourceName,
			Namespace: "default",
		}
		broadcasttrigger := &eventingv1alpha1.BroadcastTrigger{}

		BeforeEach(func() {
			By("creating the custom resource for the Kind BroadcastTrigger")
			err := k8sClient.Get(ctx, typeNamespacedName, broadcasttrigger)
			if err != nil && errors.IsNotFound(err) {
				resource := &eventingv1alpha1.BroadcastTrigger{
					ObjectMeta: metav1.ObjectMeta{
						Name:      resourceName,
						Namespace: "default",
					},
					Spec: eventingv1alpha1.BroadcastTriggerSpec{
						Broker: "default",
						Target: eventingv1alpha1.TargetSpec{
							Selector: &metav1.LabelSelector{
								MatchLabels: map[string]string{
									"app": "test-app",
								},
							},
							Port: 8080,
							Path: "/events",
						},
					},
				}
				Expect(k8sClient.Create(ctx, resource)).To(Succeed())
			}
		})

		AfterEach(func() {
			// TODO(user): Cleanup logic after each test, like removing the resource instance.
			resource := &eventingv1alpha1.BroadcastTrigger{}
			err := k8sClient.Get(ctx, typeNamespacedName, resource)
			Expect(err).NotTo(HaveOccurred())

			By("Cleanup the specific resource instance BroadcastTrigger")
			Expect(k8sClient.Delete(ctx, resource)).To(Succeed())
		})

		It("should successfully reconcile the resource", func() {
			By("Reconciling the created resource")

			// Create mock services
			podDiscovery := broadcaster.NewPodDiscoveryService(k8sClient, GinkgoLogr)
			eventReceiver := broadcaster.NewEventReceiver(k8sClient, GinkgoLogr, podDiscovery)

			controllerReconciler := &BroadcastTriggerReconciler{
				Client:        k8sClient,
				Scheme:        k8sClient.Scheme(),
				PodDiscovery:  podDiscovery,
				EventReceiver: eventReceiver,
			}

			_, err := controllerReconciler.Reconcile(ctx, reconcile.Request{
				NamespacedName: typeNamespacedName,
			})
			Expect(err).NotTo(HaveOccurred())

			// Verify the resource was updated with status
			err = k8sClient.Get(ctx, typeNamespacedName, broadcasttrigger)
			Expect(err).NotTo(HaveOccurred())
			Expect(broadcasttrigger.Status.ObservedGeneration).To(Equal(broadcasttrigger.Generation))
		})
	})
})
