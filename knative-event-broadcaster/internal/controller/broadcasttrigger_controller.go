/*
Copyright 2025 Alauda DevOps.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/log"

	eventingv1alpha1 "github.com/AlaudaDevops/experimental/knative-event-broadcaster/api/v1alpha1"
	"github.com/AlaudaDevops/experimental/knative-event-broadcaster/internal/broadcaster"
)

const (
	BroadcastTriggerFinalizer = "eventing.broadcast.io/finalizer"

	// Condition types
	ConditionReady           = "Ready"
	ConditionPodsDiscovered  = "PodsDiscovered"
	ConditionTriggerCreated  = "TriggerCreated"
)

// BroadcastTriggerReconciler reconciles a BroadcastTrigger object
type BroadcastTriggerReconciler struct {
	client.Client
	Scheme          *runtime.Scheme
	PodDiscovery    *broadcaster.PodDiscoveryService
	EventReceiver   *broadcaster.EventReceiver
}

//+kubebuilder:rbac:groups=eventing.broadcast.io,resources=broadcasttriggers,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=eventing.broadcast.io,resources=broadcasttriggers/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=eventing.broadcast.io,resources=broadcasttriggers/finalizers,verbs=update
//+kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch
//+kubebuilder:rbac:groups="",resources=services,verbs=get;list;watch
//+kubebuilder:rbac:groups=apps,resources=deployments;replicasets,verbs=get;list;watch
//+kubebuilder:rbac:groups=eventing.knative.dev,resources=brokers;triggers,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups="",resources=events,verbs=create

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
func (r *BroadcastTriggerReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// Fetch the BroadcastTrigger instance
	var broadcastTrigger eventingv1alpha1.BroadcastTrigger
	if err := r.Client.Get(ctx, req.NamespacedName, &broadcastTrigger); err != nil {
		if errors.IsNotFound(err) {
			// Request object not found, could have been deleted after reconcile request.
			logger.Info("BroadcastTrigger resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		logger.Error(err, "Failed to get BroadcastTrigger")
		return ctrl.Result{}, err
	}

	// Handle deletion
	if broadcastTrigger.ObjectMeta.DeletionTimestamp != nil {
		return r.handleDeletion(ctx, &broadcastTrigger)
	}

	// Add finalizer if not present
	if !controllerutil.ContainsFinalizer(&broadcastTrigger, BroadcastTriggerFinalizer) {
		controllerutil.AddFinalizer(&broadcastTrigger, BroadcastTriggerFinalizer)
		if err := r.Client.Update(ctx, &broadcastTrigger); err != nil {
			logger.Error(err, "Failed to add finalizer")
			return ctrl.Result{}, err
		}
		return ctrl.Result{Requeue: true}, nil
	}

	// Reconcile the BroadcastTrigger
	result, err := r.reconcileBroadcastTrigger(ctx, &broadcastTrigger)
	if err != nil {
		logger.Error(err, "Failed to reconcile BroadcastTrigger")
		r.updateCondition(&broadcastTrigger, ConditionReady, metav1.ConditionFalse, "ReconcileError", err.Error())
	} else {
		r.updateCondition(&broadcastTrigger, ConditionReady, metav1.ConditionTrue, "ReconcileSuccess", "BroadcastTrigger reconciled successfully")
	}

	// Update status
	if statusErr := r.Client.Status().Update(ctx, &broadcastTrigger); statusErr != nil {
		logger.Error(statusErr, "Failed to update BroadcastTrigger status")
		if err == nil {
			err = statusErr
		}
	}

	return result, err
}

func (r *BroadcastTriggerReconciler) reconcileBroadcastTrigger(ctx context.Context, bt *eventingv1alpha1.BroadcastTrigger) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// Update observed generation
	bt.Status.ObservedGeneration = bt.ObjectMeta.Generation

	// Register with event receiver
	if err := r.EventReceiver.RegisterBroadcastTrigger(bt); err != nil {
		logger.Error(err, "Failed to register BroadcastTrigger with event receiver")
		r.updateCondition(bt, ConditionReady, metav1.ConditionFalse, "RegistrationError", err.Error())
		return ctrl.Result{RequeueAfter: time.Minute}, err
	}

	// Start pod discovery for this trigger
	if err := r.PodDiscovery.StartWatching(ctx, bt, func(pods []eventingv1alpha1.PodReference) {
		// Update status when pods change
		bt.Status.TargetPods = pods
		r.updateCondition(bt, ConditionPodsDiscovered, metav1.ConditionTrue, "PodsDiscovered", fmt.Sprintf("Discovered %d target pods", len(pods)))

		// Update status in background
		go func() {
			if err := r.Client.Status().Update(context.Background(), bt); err != nil {
				logger.Error(err, "Failed to update status after pod discovery")
			}
		}()
	}); err != nil {
		logger.Error(err, "Failed to start pod watching")
		r.updateCondition(bt, ConditionPodsDiscovered, metav1.ConditionFalse, "WatchError", err.Error())
		return ctrl.Result{RequeueAfter: time.Minute}, err
	}

	// Discover initial target pods
	pods, err := r.discoverTargetPods(ctx, bt)
	if err != nil {
		logger.Error(err, "Failed to discover target pods")
		r.updateCondition(bt, ConditionPodsDiscovered, metav1.ConditionFalse, "DiscoveryError", err.Error())
		return ctrl.Result{RequeueAfter: time.Minute}, err
	}

	// Update status with discovered pods
	bt.Status.TargetPods = pods
	r.updateCondition(bt, ConditionPodsDiscovered, metav1.ConditionTrue, "PodsDiscovered", fmt.Sprintf("Discovered %d target pods", len(pods)))

	logger.Info("BroadcastTrigger reconciled successfully", "targetPods", len(pods))
	return ctrl.Result{RequeueAfter: time.Minute * 5}, nil
}

func (r *BroadcastTriggerReconciler) discoverTargetPods(ctx context.Context, bt *eventingv1alpha1.BroadcastTrigger) ([]eventingv1alpha1.PodReference, error) {
	// Determine target namespace
	targetNamespace := bt.Spec.Target.Namespace
	if targetNamespace == "" {
		targetNamespace = bt.ObjectMeta.Namespace
	}

	// List pods matching the selector
	var podList corev1.PodList
	selector, err := metav1.LabelSelectorAsSelector(bt.Spec.Target.Selector)
	if err != nil {
		return nil, fmt.Errorf("invalid label selector: %w", err)
	}

	listOpts := &client.ListOptions{
		Namespace:     targetNamespace,
		LabelSelector: selector,
	}

	if err := r.Client.List(ctx, &podList, listOpts); err != nil {
		return nil, fmt.Errorf("failed to list pods: %w", err)
	}

	// Convert to PodReference slice
	var podRefs []eventingv1alpha1.PodReference
	for _, pod := range podList.Items {
		// Only include running pods with an IP
		if pod.Status.Phase == corev1.PodRunning && pod.Status.PodIP != "" {
			podRef := eventingv1alpha1.PodReference{
				Name:      pod.Name,
				Namespace: pod.Namespace,
				IP:        pod.Status.PodIP,
				Ready:     isPodReady(&pod),
			}
			podRefs = append(podRefs, podRef)
		}
	}

	return podRefs, nil
}

func (r *BroadcastTriggerReconciler) handleDeletion(ctx context.Context, bt *eventingv1alpha1.BroadcastTrigger) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	if controllerutil.ContainsFinalizer(bt, BroadcastTriggerFinalizer) {
		// Clean up resources
		logger.Info("Cleaning up BroadcastTrigger resources")

		// Stop pod watching
		r.PodDiscovery.StopWatching(bt)

		// Unregister from event receiver
		r.EventReceiver.UnregisterBroadcastTrigger(bt)

		// Remove finalizer
		controllerutil.RemoveFinalizer(bt, BroadcastTriggerFinalizer)
		if err := r.Client.Update(ctx, bt); err != nil {
			logger.Error(err, "Failed to remove finalizer")
			return ctrl.Result{}, err
		}
	}

	return ctrl.Result{}, nil
}

func (r *BroadcastTriggerReconciler) updateCondition(bt *eventingv1alpha1.BroadcastTrigger, conditionType string, status metav1.ConditionStatus, reason, message string) {
	condition := metav1.Condition{
		Type:               conditionType,
		Status:             status,
		LastTransitionTime: metav1.NewTime(time.Now()),
		Reason:             reason,
		Message:            message,
	}

	// Find existing condition
	for i, existingCondition := range bt.Status.Conditions {
		if existingCondition.Type == conditionType {
			if existingCondition.Status != status {
				bt.Status.Conditions[i] = condition
			}
			return
		}
	}

	// Add new condition
	bt.Status.Conditions = append(bt.Status.Conditions, condition)
}

func isPodReady(pod *corev1.Pod) bool {
	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodReady {
			return condition.Status == corev1.ConditionTrue
		}
	}
	return false
}

// SetupWithManager sets up the controller with the Manager.
func (r *BroadcastTriggerReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&eventingv1alpha1.BroadcastTrigger{}).
		Owns(&corev1.Pod{}).
		Complete(r)
}
