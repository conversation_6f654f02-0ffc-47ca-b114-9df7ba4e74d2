/*
Copyright 2025 Alauda DevOps.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package broadcaster

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	cloudevents "github.com/cloudevents/sdk-go/v2"
	"github.com/cloudevents/sdk-go/v2/event"
	"github.com/go-logr/logr"
	"sigs.k8s.io/controller-runtime/pkg/client"

	eventingv1alpha1 "github.com/AlaudaDevops/experimental/knative-event-broadcaster/api/v1alpha1"
)

// EventReceiver receives events from Knative and broadcasts them to target pods
type EventReceiver struct {
	client            client.Client
	logger            logr.Logger
	podDiscovery      *PodDiscoveryService
	distributors      map[string]*EventDistributor
	broadcastTriggers map[string]*eventingv1alpha1.BroadcastTrigger
	mutex             sync.RWMutex
	server            *http.Server
}

// NewEventReceiver creates a new EventReceiver
func NewEventReceiver(client client.Client, logger logr.Logger, podDiscovery *PodDiscoveryService) *EventReceiver {
	return &EventReceiver{
		client:            client,
		logger:            logger,
		podDiscovery:      podDiscovery,
		distributors:      make(map[string]*EventDistributor),
		broadcastTriggers: make(map[string]*eventingv1alpha1.BroadcastTrigger),
	}
}

// Start starts the event receiver HTTP server
func (er *EventReceiver) Start(ctx context.Context, port int) error {
	mux := http.NewServeMux()
	mux.HandleFunc("/", er.handleEvent)
	mux.HandleFunc("/health", er.handleHealth)

	er.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: mux,
	}

	er.logger.Info("Starting event receiver server", "port", port)

	// Start server in a goroutine
	go func() {
		if err := er.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			er.logger.Error(err, "Event receiver server failed")
		}
	}()

	// Wait for context cancellation
	<-ctx.Done()

	// Graceful shutdown
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	er.logger.Info("Shutting down event receiver server")
	return er.server.Shutdown(shutdownCtx)
}

// RegisterBroadcastTrigger registers a BroadcastTrigger for event handling
func (er *EventReceiver) RegisterBroadcastTrigger(bt *eventingv1alpha1.BroadcastTrigger) error {
	key := fmt.Sprintf("%s/%s", bt.Namespace, bt.Name)

	er.mutex.Lock()
	defer er.mutex.Unlock()

	// Create event distributor for this trigger
	distributor := NewEventDistributor(er.logger.WithValues("trigger", key), bt.Spec.RateLimit)
	
	er.distributors[key] = distributor
	er.broadcastTriggers[key] = bt.DeepCopy()

	er.logger.Info("Registered BroadcastTrigger", "trigger", key)
	return nil
}

// UnregisterBroadcastTrigger unregisters a BroadcastTrigger
func (er *EventReceiver) UnregisterBroadcastTrigger(bt *eventingv1alpha1.BroadcastTrigger) {
	key := fmt.Sprintf("%s/%s", bt.Namespace, bt.Name)

	er.mutex.Lock()
	defer er.mutex.Unlock()

	delete(er.distributors, key)
	delete(er.broadcastTriggers, key)

	er.logger.Info("Unregistered BroadcastTrigger", "trigger", key)
}

// handleEvent handles incoming CloudEvents
func (er *EventReceiver) handleEvent(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	
	// Parse CloudEvent from HTTP request
	ce, err := er.parseCloudEvent(r)
	if err != nil {
		er.logger.Error(err, "Failed to parse CloudEvent")
		http.Error(w, fmt.Sprintf("Invalid CloudEvent: %v", err), http.StatusBadRequest)
		return
	}

	er.logger.Info("Received CloudEvent", "type", ce.Type(), "source", ce.Source(), "id", ce.ID())

	// Find matching BroadcastTriggers
	matchingTriggers := er.findMatchingTriggers(ce)
	if len(matchingTriggers) == 0 {
		er.logger.V(1).Info("No matching BroadcastTriggers found for event", "type", ce.Type())
		w.WriteHeader(http.StatusOK)
		return
	}

	// Broadcast to all matching triggers
	var allResults []DeliveryResult
	for _, trigger := range matchingTriggers {
		results, err := er.broadcastToTrigger(ctx, ce, trigger)
		if err != nil {
			er.logger.Error(err, "Failed to broadcast to trigger", "trigger", trigger.Name)
			continue
		}
		allResults = append(allResults, results...)
	}

	// Update metrics and status
	er.updateDeliveryMetrics(allResults)

	// Return success response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	
	response := map[string]interface{}{
		"delivered": len(allResults),
		"triggers":  len(matchingTriggers),
	}
	json.NewEncoder(w).Encode(response)
}

// handleHealth handles health check requests
func (er *EventReceiver) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"status": "healthy"})
}

// parseCloudEvent parses a CloudEvent from an HTTP request
func (er *EventReceiver) parseCloudEvent(r *http.Request) (event.Event, error) {
	// Try to parse as structured CloudEvent (JSON)
	if r.Header.Get("Content-Type") == "application/json" {
		var ce event.Event
		if err := json.NewDecoder(r.Body).Decode(&ce); err == nil {
			return ce, nil
		}
	}

	// Try to parse as binary CloudEvent (headers)
	ce := event.New()
	
	// Required headers
	if specVersion := r.Header.Get("Ce-Specversion"); specVersion != "" {
		ce.SetSpecVersion(specVersion)
	} else {
		return ce, fmt.Errorf("missing Ce-Specversion header")
	}
	
	if eventType := r.Header.Get("Ce-Type"); eventType != "" {
		ce.SetType(eventType)
	} else {
		return ce, fmt.Errorf("missing Ce-Type header")
	}
	
	if source := r.Header.Get("Ce-Source"); source != "" {
		ce.SetSource(source)
	} else {
		return ce, fmt.Errorf("missing Ce-Source header")
	}
	
	if id := r.Header.Get("Ce-Id"); id != "" {
		ce.SetID(id)
	} else {
		return ce, fmt.Errorf("missing Ce-Id header")
	}

	// Optional headers
	if timeStr := r.Header.Get("Ce-Time"); timeStr != "" {
		if t, err := time.Parse(time.RFC3339, timeStr); err == nil {
			ce.SetTime(t)
		}
	}

	// Extension attributes
	for name, values := range r.Header {
		if len(values) > 0 && len(name) > 3 && name[:3] == "Ce-" {
			attrName := name[3:]
			if attrName != "specversion" && attrName != "type" && attrName != "source" && attrName != "id" && attrName != "time" {
				ce.SetExtension(attrName, values[0])
			}
		}
	}

	// Set data if present
	if r.ContentLength > 0 {
		var data interface{}
		if err := json.NewDecoder(r.Body).Decode(&data); err == nil {
			ce.SetData(cloudevents.ApplicationJSON, data)
		}
	}

	return ce, nil
}

// findMatchingTriggers finds BroadcastTriggers that match the event
func (er *EventReceiver) findMatchingTriggers(ce event.Event) []*eventingv1alpha1.BroadcastTrigger {
	er.mutex.RLock()
	defer er.mutex.RUnlock()

	var matching []*eventingv1alpha1.BroadcastTrigger
	
	for _, trigger := range er.broadcastTriggers {
		if er.eventMatchesFilter(ce, trigger.Spec.Filter) {
			matching = append(matching, trigger)
		}
	}

	return matching
}

// eventMatchesFilter checks if an event matches the trigger filter
func (er *EventReceiver) eventMatchesFilter(ce event.Event, filter *eventingv1alpha1.TriggerFilter) bool {
	if filter == nil {
		return true // No filter means match all
	}

	// Check attribute filters
	if filter.Attributes != nil {
		for key, expectedValue := range filter.Attributes {
			var actualValue string
			
			switch key {
			case "type":
				actualValue = ce.Type()
			case "source":
				actualValue = ce.Source()
			case "id":
				actualValue = ce.ID()
			default:
				if ext, ok := ce.Extensions()[key]; ok {
					if str, ok := ext.(string); ok {
						actualValue = str
					}
				}
			}
			
			if actualValue != expectedValue {
				return false
			}
		}
	}

	// TODO: Implement CEL expression evaluation if needed
	if filter.CEL != "" {
		er.logger.V(1).Info("CEL filtering not yet implemented", "expression", filter.CEL)
	}

	return true
}

// broadcastToTrigger broadcasts an event to all pods for a specific trigger
func (er *EventReceiver) broadcastToTrigger(ctx context.Context, ce event.Event, trigger *eventingv1alpha1.BroadcastTrigger) ([]DeliveryResult, error) {
	// Get current pods for this trigger
	pods := er.podDiscovery.GetCurrentPods(trigger)
	if len(pods) == 0 {
		er.logger.V(1).Info("No target pods found for trigger", "trigger", trigger.Name)
		return nil, nil
	}

	// Get distributor for this trigger
	key := fmt.Sprintf("%s/%s", trigger.Namespace, trigger.Name)
	
	er.mutex.RLock()
	distributor, exists := er.distributors[key]
	er.mutex.RUnlock()
	
	if !exists {
		return nil, fmt.Errorf("no distributor found for trigger %s", key)
	}

	// Broadcast the event
	return distributor.BroadcastEvent(ctx, ce, pods, &trigger.Spec)
}

// updateDeliveryMetrics updates delivery metrics based on results
func (er *EventReceiver) updateDeliveryMetrics(results []DeliveryResult) {
	successful := 0
	failed := 0
	
	for _, result := range results {
		if result.Success {
			successful++
		} else {
			failed++
			er.logger.V(1).Info("Event delivery failed", "pod", result.Pod.Name, "error", result.Error)
		}
	}

	er.logger.Info("Event delivery completed", "successful", successful, "failed", failed)
	
	// TODO: Update Prometheus metrics
	// TODO: Update BroadcastTrigger status
}
