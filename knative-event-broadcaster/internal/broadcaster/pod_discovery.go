/*
Copyright 2025 Alauda DevOps.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package broadcaster

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"sigs.k8s.io/controller-runtime/pkg/client"

	eventingv1alpha1 "github.com/AlaudaDevops/experimental/knative-event-broadcaster/api/v1alpha1"
)

// PodDiscoveryService manages the discovery and tracking of target pods
type PodDiscoveryService struct {
	client   client.Client
	logger   logr.Logger
	watchers map[string]*PodWatcher
	mutex    sync.RWMutex
}

// PodWatcher watches pods for a specific BroadcastTrigger
type PodWatcher struct {
	broadcastTrigger *eventingv1alpha1.BroadcastTrigger
	pods             map[string]eventingv1alpha1.PodReference
	mutex            sync.RWMutex
	stopCh           chan struct{}
	updateCallback   func([]eventingv1alpha1.PodReference)
}

// NewPodDiscoveryService creates a new PodDiscoveryService
func NewPodDiscoveryService(client client.Client, logger logr.Logger) *PodDiscoveryService {
	return &PodDiscoveryService{
		client:   client,
		logger:   logger,
		watchers: make(map[string]*PodWatcher),
	}
}

// StartWatching starts watching pods for a BroadcastTrigger
func (pds *PodDiscoveryService) StartWatching(ctx context.Context, bt *eventingv1alpha1.BroadcastTrigger, updateCallback func([]eventingv1alpha1.PodReference)) error {
	key := fmt.Sprintf("%s/%s", bt.Namespace, bt.Name)

	pds.mutex.Lock()
	defer pds.mutex.Unlock()

	// Stop existing watcher if any
	if existingWatcher, exists := pds.watchers[key]; exists {
		existingWatcher.Stop()
	}

	// Create new watcher
	watcher := &PodWatcher{
		broadcastTrigger: bt,
		pods:             make(map[string]eventingv1alpha1.PodReference),
		stopCh:           make(chan struct{}),
		updateCallback:   updateCallback,
	}

	pds.watchers[key] = watcher

	// Start watching in a goroutine
	go pds.watchPods(ctx, watcher)

	pds.logger.Info("Started watching pods for BroadcastTrigger", "trigger", key)
	return nil
}

// StopWatching stops watching pods for a BroadcastTrigger
func (pds *PodDiscoveryService) StopWatching(bt *eventingv1alpha1.BroadcastTrigger) {
	key := fmt.Sprintf("%s/%s", bt.Namespace, bt.Name)

	pds.mutex.Lock()
	defer pds.mutex.Unlock()

	if watcher, exists := pds.watchers[key]; exists {
		watcher.Stop()
		delete(pds.watchers, key)
		pds.logger.Info("Stopped watching pods for BroadcastTrigger", "trigger", key)
	}
}

// GetCurrentPods returns the current list of pods for a BroadcastTrigger
func (pds *PodDiscoveryService) GetCurrentPods(bt *eventingv1alpha1.BroadcastTrigger) []eventingv1alpha1.PodReference {
	key := fmt.Sprintf("%s/%s", bt.Namespace, bt.Name)

	pds.mutex.RLock()
	defer pds.mutex.RUnlock()

	if watcher, exists := pds.watchers[key]; exists {
		return watcher.GetPods()
	}

	return nil
}

// watchPods watches for pod changes and updates the pod list
func (pds *PodDiscoveryService) watchPods(ctx context.Context, watcher *PodWatcher) {
	bt := watcher.broadcastTrigger

	// Determine target namespace
	targetNamespace := bt.Spec.Target.Namespace
	if targetNamespace == "" {
		targetNamespace = bt.Namespace
	}

	// Create label selector
	selector, err := metav1.LabelSelectorAsSelector(bt.Spec.Target.Selector)
	if err != nil {
		pds.logger.Error(err, "Invalid label selector", "trigger", bt.Name)
		return
	}

	// Initial discovery
	if err := pds.discoverInitialPods(ctx, watcher, targetNamespace, selector); err != nil {
		pds.logger.Error(err, "Failed to discover initial pods", "trigger", bt.Name)
	}

	// Start watching for changes
	watchOpts := metav1.ListOptions{
		LabelSelector: selector.String(),
	}

	for {
		select {
		case <-watcher.stopCh:
			pds.logger.Info("Pod watcher stopped", "trigger", bt.Name)
			return
		case <-ctx.Done():
			pds.logger.Info("Pod watcher context cancelled", "trigger", bt.Name)
			return
		default:
			if err := pds.watchPodChanges(ctx, watcher, targetNamespace, watchOpts); err != nil {
				pds.logger.Error(err, "Error watching pod changes", "trigger", bt.Name)
				// Wait before retrying
				select {
				case <-time.After(5 * time.Second):
				case <-watcher.stopCh:
					return
				case <-ctx.Done():
					return
				}
			}
		}
	}
}

// discoverInitialPods discovers the initial set of pods
func (pds *PodDiscoveryService) discoverInitialPods(ctx context.Context, watcher *PodWatcher, namespace string, selector labels.Selector) error {
	var podList corev1.PodList
	listOpts := &client.ListOptions{
		Namespace:     namespace,
		LabelSelector: selector,
	}

	if err := pds.client.List(ctx, &podList, listOpts); err != nil {
		return fmt.Errorf("failed to list pods: %w", err)
	}

	watcher.mutex.Lock()
	defer watcher.mutex.Unlock()

	// Clear existing pods
	watcher.pods = make(map[string]eventingv1alpha1.PodReference)

	// Add discovered pods
	for _, pod := range podList.Items {
		if pds.isPodEligible(&pod) {
			podRef := pds.createPodReference(&pod)
			watcher.pods[pod.Name] = podRef
		}
	}

	// Notify callback
	if watcher.updateCallback != nil {
		pods := make([]eventingv1alpha1.PodReference, 0, len(watcher.pods))
		for _, pod := range watcher.pods {
			pods = append(pods, pod)
		}
		watcher.updateCallback(pods)
	}

	pds.logger.Info("Discovered initial pods", "trigger", watcher.broadcastTrigger.Name, "count", len(watcher.pods))
	return nil
}

// watchPodChanges watches for pod changes using periodic polling
func (pds *PodDiscoveryService) watchPodChanges(ctx context.Context, watcher *PodWatcher, namespace string, watchOpts metav1.ListOptions) error {
	ticker := time.NewTicker(10 * time.Second) // Poll every 10 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// Re-discover pods periodically
			selector, err := metav1.LabelSelectorAsSelector(watcher.broadcastTrigger.Spec.Target.Selector)
			if err != nil {
				pds.logger.Error(err, "Invalid label selector during watch")
				continue
			}

			if err := pds.discoverInitialPods(ctx, watcher, namespace, selector); err != nil {
				pds.logger.Error(err, "Failed to rediscover pods during watch")
			}

		case <-watcher.stopCh:
			return nil
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}



// isPodEligible checks if a pod is eligible for event delivery
func (pds *PodDiscoveryService) isPodEligible(pod *corev1.Pod) bool {
	// Pod must be running and have an IP
	return pod.Status.Phase == corev1.PodRunning && pod.Status.PodIP != ""
}

// createPodReference creates a PodReference from a Pod
func (pds *PodDiscoveryService) createPodReference(pod *corev1.Pod) eventingv1alpha1.PodReference {
	return eventingv1alpha1.PodReference{
		Name:      pod.Name,
		Namespace: pod.Namespace,
		IP:        pod.Status.PodIP,
		Ready:     pds.isPodReady(pod),
	}
}

// isPodReady checks if a pod is ready
func (pds *PodDiscoveryService) isPodReady(pod *corev1.Pod) bool {
	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodReady {
			return condition.Status == corev1.ConditionTrue
		}
	}
	return false
}

// Stop stops the watcher
func (pw *PodWatcher) Stop() {
	close(pw.stopCh)
}

// GetPods returns the current list of pods
func (pw *PodWatcher) GetPods() []eventingv1alpha1.PodReference {
	pw.mutex.RLock()
	defer pw.mutex.RUnlock()

	pods := make([]eventingv1alpha1.PodReference, 0, len(pw.pods))
	for _, pod := range pw.pods {
		pods = append(pods, pod)
	}
	return pods
}
