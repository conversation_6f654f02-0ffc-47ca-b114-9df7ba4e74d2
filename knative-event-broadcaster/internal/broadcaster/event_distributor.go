/*
Copyright 2025 Alauda DevOps.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package broadcaster

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/cloudevents/sdk-go/v2/event"
	"github.com/go-logr/logr"
	"golang.org/x/time/rate"

	eventingv1alpha1 "github.com/AlaudaDevops/experimental/knative-event-broadcaster/api/v1alpha1"
)

// EventDistributor handles the distribution of events to multiple pod instances
type EventDistributor struct {
	logger     logr.Logger
	httpClient *http.Client
	rateLimiter *rate.Limiter
}

// NewEventDistributor creates a new EventDistributor
func NewEventDistributor(logger logr.Logger, rateLimit *eventingv1alpha1.RateLimitSpec) *EventDistributor {
	// Configure HTTP client with reasonable timeouts
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	// Configure rate limiter
	var rateLimiter *rate.Limiter
	if rateLimit != nil {
		rateLimiter = rate.NewLimiter(
			rate.Limit(rateLimit.RequestsPerSecond),
			int(rateLimit.Burst),
		)
	}

	return &EventDistributor{
		logger:      logger,
		httpClient:  httpClient,
		rateLimiter: rateLimiter,
	}
}

// DeliveryResult represents the result of delivering an event to a pod
type DeliveryResult struct {
	Pod     eventingv1alpha1.PodReference
	Success bool
	Error   error
	Latency time.Duration
}

// BroadcastEvent distributes an event to all specified pods
func (ed *EventDistributor) BroadcastEvent(ctx context.Context, ce event.Event, pods []eventingv1alpha1.PodReference, config *eventingv1alpha1.BroadcastTriggerSpec) ([]DeliveryResult, error) {
	if len(pods) == 0 {
		return nil, fmt.Errorf("no target pods specified")
	}

	ed.logger.Info("Broadcasting event to pods", "eventType", ce.Type(), "podCount", len(pods))

	// Determine delivery strategy based on ordering requirements
	if config.Delivery != nil && config.Delivery.Ordering == "strict" {
		return ed.deliverSequentially(ctx, ce, pods, config)
	}
	return ed.deliverParallel(ctx, ce, pods, config)
}

// deliverParallel delivers events to all pods in parallel
func (ed *EventDistributor) deliverParallel(ctx context.Context, ce event.Event, pods []eventingv1alpha1.PodReference, config *eventingv1alpha1.BroadcastTriggerSpec) ([]DeliveryResult, error) {
	results := make([]DeliveryResult, len(pods))
	var wg sync.WaitGroup

	for i, pod := range pods {
		wg.Add(1)
		go func(index int, podRef eventingv1alpha1.PodReference) {
			defer wg.Done()
			results[index] = ed.deliverToPod(ctx, ce, podRef, config)
		}(i, pod)
	}

	wg.Wait()
	return results, nil
}

// deliverSequentially delivers events to pods one by one
func (ed *EventDistributor) deliverSequentially(ctx context.Context, ce event.Event, pods []eventingv1alpha1.PodReference, config *eventingv1alpha1.BroadcastTriggerSpec) ([]DeliveryResult, error) {
	results := make([]DeliveryResult, len(pods))

	for i, pod := range pods {
		results[i] = ed.deliverToPod(ctx, ce, pod, config)

		// Stop on first failure if strict ordering is required
		if !results[i].Success {
			ed.logger.Error(results[i].Error, "Failed to deliver event in strict order", "pod", pod.Name)
			// Continue with remaining pods but mark the ordering as broken
		}
	}

	return results, nil
}

// deliverToPod delivers an event to a specific pod with retry logic
func (ed *EventDistributor) deliverToPod(ctx context.Context, ce event.Event, pod eventingv1alpha1.PodReference, config *eventingv1alpha1.BroadcastTriggerSpec) DeliveryResult {
	start := time.Now()

	// Apply rate limiting if configured
	if ed.rateLimiter != nil {
		if err := ed.rateLimiter.Wait(ctx); err != nil {
			return DeliveryResult{
				Pod:     pod,
				Success: false,
				Error:   fmt.Errorf("rate limit exceeded: %w", err),
				Latency: time.Since(start),
			}
		}
	}

	// Skip pods that are not ready
	if !pod.Ready {
		return DeliveryResult{
			Pod:     pod,
			Success: false,
			Error:   fmt.Errorf("pod is not ready"),
			Latency: time.Since(start),
		}
	}

	// Determine retry configuration
	maxRetries := int32(3)
	timeout := 30 * time.Second
	backoffDelay := 1 * time.Second

	if config.Delivery != nil {
		if config.Delivery.Retry > 0 {
			maxRetries = config.Delivery.Retry
		}
		if config.Delivery.Timeout != nil {
			timeout = config.Delivery.Timeout.Duration
		}
		if config.Delivery.BackoffDelay != nil {
			backoffDelay = config.Delivery.BackoffDelay.Duration
		}
	}

	// Attempt delivery with retries
	var lastErr error
	for attempt := int32(0); attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			// Apply backoff delay
			delay := ed.calculateBackoffDelay(attempt, backoffDelay, config.Delivery)
			select {
			case <-ctx.Done():
				return DeliveryResult{
					Pod:     pod,
					Success: false,
					Error:   ctx.Err(),
					Latency: time.Since(start),
				}
			case <-time.After(delay):
			}
		}

		// Create delivery context with timeout
		deliveryCtx, cancel := context.WithTimeout(ctx, timeout)
		err := ed.sendEventToPod(deliveryCtx, ce, pod, config)
		cancel()

		if err == nil {
			return DeliveryResult{
				Pod:     pod,
				Success: true,
				Error:   nil,
				Latency: time.Since(start),
			}
		}

		lastErr = err
		ed.logger.Info("Event delivery attempt failed", "pod", pod.Name, "attempt", attempt+1, "error", err)
	}

	return DeliveryResult{
		Pod:     pod,
		Success: false,
		Error:   fmt.Errorf("failed after %d attempts: %w", maxRetries+1, lastErr),
		Latency: time.Since(start),
	}
}

// sendEventToPod sends the actual HTTP request to the pod
func (ed *EventDistributor) sendEventToPod(ctx context.Context, ce event.Event, pod eventingv1alpha1.PodReference, config *eventingv1alpha1.BroadcastTriggerSpec) error {
	// Determine target URL
	port := int32(8080)
	path := "/"

	if config.Target.Port > 0 {
		port = config.Target.Port
	}
	if config.Target.Path != "" {
		path = config.Target.Path
	}

	url := fmt.Sprintf("http://%s:%d%s", pod.IP, port, path)

	// Convert CloudEvent to HTTP request
	req, err := ed.createHTTPRequest(ctx, ce, url)
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Send the request
	resp, err := ed.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("HTTP request failed with status %d: %s", resp.StatusCode, string(body))
	}

	ed.logger.V(1).Info("Event delivered successfully", "pod", pod.Name, "url", url, "status", resp.StatusCode)
	return nil
}

// createHTTPRequest creates an HTTP request from a CloudEvent
func (ed *EventDistributor) createHTTPRequest(ctx context.Context, ce event.Event, url string) (*http.Request, error) {
	// Serialize the CloudEvent to JSON
	data, err := ce.MarshalJSON()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal CloudEvent: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set CloudEvent headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Ce-Specversion", ce.SpecVersion())
	req.Header.Set("Ce-Type", ce.Type())
	req.Header.Set("Ce-Source", ce.Source())
	req.Header.Set("Ce-Id", ce.ID())

	if !ce.Time().IsZero() {
		req.Header.Set("Ce-Time", ce.Time().Format(time.RFC3339))
	}

	// Add custom attributes as headers
	for name, value := range ce.Extensions() {
		if str, ok := value.(string); ok {
			req.Header.Set(fmt.Sprintf("Ce-%s", name), str)
		}
	}

	return req, nil
}

// calculateBackoffDelay calculates the delay for retry attempts
func (ed *EventDistributor) calculateBackoffDelay(attempt int32, baseDelay time.Duration, delivery *eventingv1alpha1.DeliverySpec) time.Duration {
	if delivery == nil || delivery.BackoffPolicy != "exponential" {
		// Linear backoff
		return time.Duration(attempt) * baseDelay
	}

	// Exponential backoff
	multiplier := int64(1)
	for i := int32(0); i < attempt; i++ {
		multiplier *= 2
	}

	delay := time.Duration(multiplier) * baseDelay

	// Cap at 5 minutes
	maxDelay := 5 * time.Minute
	if delay > maxDelay {
		delay = maxDelay
	}

	return delay
}
