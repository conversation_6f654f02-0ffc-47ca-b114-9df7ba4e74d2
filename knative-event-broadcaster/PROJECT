# Code generated by tool. DO NOT EDIT.
# This file is used to track the info used to scaffold your project
# and allow the plugins properly work.
# More info: https://book.kubebuilder.io/reference/project-config.html
domain: broadcast.io
layout:
- go.kubebuilder.io/v4
projectName: knative-event-broadcaster
repo: github.com/AlaudaDevops/experimental/knative-event-broadcaster
resources:
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: broadcast.io
  group: eventing
  kind: BroadcastTrigger
  path: github.com/AlaudaDevops/experimental/knative-event-broadcaster/api/v1alpha1
  version: v1alpha1
version: "3"
