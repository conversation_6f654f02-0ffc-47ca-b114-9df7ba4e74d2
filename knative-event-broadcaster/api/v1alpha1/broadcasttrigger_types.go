/*
Copyright 2025 Alauda DevOps.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	duckv1 "knative.dev/pkg/apis/duck/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// BroadcastTriggerSpec defines the desired state of BroadcastTrigger
type BroadcastTriggerSpec struct {
	// Broker is the name of the Knative broker to subscribe to
	// +kubebuilder:validation:Required
	Broker string `json:"broker"`

	// Filter defines the event filtering criteria
	// +optional
	Filter *TriggerFilter `json:"filter,omitempty"`

	// Target defines the pods to broadcast events to
	// +kubebuilder:validation:Required
	Target TargetSpec `json:"target"`

	// Delivery defines delivery configuration for event broadcasting
	// +optional
	Delivery *DeliverySpec `json:"delivery,omitempty"`

	// RateLimit defines rate limiting configuration
	// +optional
	RateLimit *RateLimitSpec `json:"rateLimit,omitempty"`
}

// TriggerFilter defines event filtering criteria (compatible with Knative Trigger)
type TriggerFilter struct {
	// Attributes filters events by their attributes
	// +optional
	Attributes map[string]string `json:"attributes,omitempty"`

	// CEL expression for advanced filtering
	// +optional
	CEL string `json:"cel,omitempty"`
}

// TargetSpec defines the target pods for event broadcasting
type TargetSpec struct {
	// Selector is a label selector for target pods
	// +kubebuilder:validation:Required
	Selector *metav1.LabelSelector `json:"selector"`

	// Namespace is the namespace to search for pods
	// If not specified, uses the same namespace as the BroadcastTrigger
	// +optional
	Namespace string `json:"namespace,omitempty"`

	// Port is the port to send events to on target pods
	// +kubebuilder:validation:Minimum=1
	// +kubebuilder:validation:Maximum=65535
	// +kubebuilder:default=8080
	Port int32 `json:"port,omitempty"`

	// Path is the HTTP path to send events to
	// +kubebuilder:default="/"
	Path string `json:"path,omitempty"`
}

// DeliverySpec defines delivery configuration
type DeliverySpec struct {
	// Retry is the number of retry attempts for failed deliveries
	// +kubebuilder:validation:Minimum=0
	// +kubebuilder:default=3
	Retry int32 `json:"retry,omitempty"`

	// Timeout is the timeout for individual event deliveries
	// +kubebuilder:default="30s"
	Timeout *metav1.Duration `json:"timeout,omitempty"`

	// BackoffPolicy defines the backoff policy for retries
	// +kubebuilder:validation:Enum=linear;exponential
	// +kubebuilder:default="exponential"
	BackoffPolicy string `json:"backoffPolicy,omitempty"`

	// BackoffDelay is the initial delay for backoff
	// +kubebuilder:default="1s"
	BackoffDelay *metav1.Duration `json:"backoffDelay,omitempty"`

	// DeadLetterSink is the destination for events that fail delivery
	// +optional
	DeadLetterSink *duckv1.Destination `json:"deadLetterSink,omitempty"`

	// Ordering defines event ordering guarantees
	// +kubebuilder:validation:Enum=none;strict
	// +kubebuilder:default="none"
	Ordering string `json:"ordering,omitempty"`
}

// RateLimitSpec defines rate limiting configuration
type RateLimitSpec struct {
	// RequestsPerSecond is the maximum number of requests per second
	// +kubebuilder:validation:Minimum=1
	// +kubebuilder:default=100
	RequestsPerSecond int32 `json:"requestsPerSecond,omitempty"`

	// Burst is the maximum burst size
	// +kubebuilder:validation:Minimum=1
	// +kubebuilder:default=200
	Burst int32 `json:"burst,omitempty"`
}

// BroadcastTriggerStatus defines the observed state of BroadcastTrigger
type BroadcastTriggerStatus struct {
	// ObservedGeneration is the generation most recently observed by the controller
	// +optional
	ObservedGeneration int64 `json:"observedGeneration,omitempty"`

	// Conditions represent the latest available observations of the BroadcastTrigger's state
	// +optional
	Conditions []metav1.Condition `json:"conditions,omitempty"`

	// TargetPods is the list of currently targeted pods
	// +optional
	TargetPods []PodReference `json:"targetPods,omitempty"`

	// EventsDelivered is the total number of events successfully delivered
	// +optional
	EventsDelivered int64 `json:"eventsDelivered,omitempty"`

	// EventsFailed is the total number of events that failed delivery
	// +optional
	EventsFailed int64 `json:"eventsFailed,omitempty"`

	// LastEventTime is the timestamp of the last event processed
	// +optional
	LastEventTime *metav1.Time `json:"lastEventTime,omitempty"`
}

// PodReference represents a reference to a target pod
type PodReference struct {
	// Name is the pod name
	Name string `json:"name"`

	// Namespace is the pod namespace
	Namespace string `json:"namespace"`

	// IP is the pod IP address
	IP string `json:"ip"`

	// Ready indicates if the pod is ready to receive events
	Ready bool `json:"ready"`
}

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:printcolumn:name="Broker",type="string",JSONPath=".spec.broker"
//+kubebuilder:printcolumn:name="Target Pods",type="integer",JSONPath=".status.targetPods[*]"
//+kubebuilder:printcolumn:name="Events Delivered",type="integer",JSONPath=".status.eventsDelivered"
//+kubebuilder:printcolumn:name="Events Failed",type="integer",JSONPath=".status.eventsFailed"
//+kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"

// BroadcastTrigger is the Schema for the broadcasttriggers API
type BroadcastTrigger struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   BroadcastTriggerSpec   `json:"spec,omitempty"`
	Status BroadcastTriggerStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// BroadcastTriggerList contains a list of BroadcastTrigger
type BroadcastTriggerList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []BroadcastTrigger `json:"items"`
}

func init() {
	SchemeBuilder.Register(&BroadcastTrigger{}, &BroadcastTriggerList{})
}
