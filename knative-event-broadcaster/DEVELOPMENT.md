# Development Guide

This guide covers how to develop and contribute to the Knative Event Broadcaster project.

## Prerequisites

- Go 1.24+
- Docker
- kubectl
- kustomize
- A Kubernetes cluster with Knative eventing installed

## Development Setup

1. Clone the repository:
```bash
git clone https://github.com/AlaudaDevops/experimental.git
cd experimental/knative-event-broadcaster
```

2. Install dependencies:
```bash
go mod download
```

3. Generate code and manifests:
```bash
make generate manifests
```

## Building and Testing

### Local Development

Run the controller locally:
```bash
make run
```

### Unit Tests

Run unit tests:
```bash
make test
```

### Integration Tests

Run integration tests (requires a Kubernetes cluster):
```bash
make test-e2e
```

### Building Container Image

Build the container image:
```bash
make docker-build IMG=your-registry/knative-event-broadcaster:latest
```

Push to registry:
```bash
make docker-push IMG=your-registry/knative-event-broadcaster:latest
```

## Deployment

### Install CRDs

```bash
make install
```

### Deploy Controller

```bash
make deploy IMG=your-registry/knative-event-broadcaster:latest
```

### Uninstall

```bash
make undeploy
make uninstall
```

## Architecture Overview

### Components

1. **BroadcastTrigger Controller**: Main controller that manages broadcasting logic
2. **Pod Discovery Service**: Discovers target pods based on label selectors
3. **Event Distributor**: Handles actual event delivery to pods
4. **Knative Integration**: Creates and manages Knative triggers

### Key Files

- `api/v1alpha1/broadcasttrigger_types.go`: CRD definitions
- `internal/controller/broadcasttrigger_controller.go`: Main controller logic
- `cmd/main.go`: Application entry point
- `config/`: Kubernetes manifests

## Development Workflow

1. Make changes to the code
2. Run tests: `make test`
3. Generate manifests: `make manifests`
4. Test locally: `make run`
5. Build and test container: `make docker-build`
6. Deploy to test cluster: `make deploy`

## Adding New Features

### Adding New Fields to BroadcastTrigger

1. Update `api/v1alpha1/broadcasttrigger_types.go`
2. Run `make generate manifests`
3. Update controller logic in `internal/controller/`
4. Add tests
5. Update documentation

### Adding New Controllers

1. Create new controller file in `internal/controller/`
2. Add to `cmd/main.go`
3. Update RBAC in `config/rbac/role.yaml`
4. Add tests

## Testing Strategy

### Unit Tests

- Test individual functions and methods
- Mock external dependencies
- Focus on business logic

### Integration Tests

- Test controller behavior with real Kubernetes API
- Use envtest for lightweight testing
- Test CRD validation and status updates

### End-to-End Tests

- Test complete workflows
- Use real Knative eventing components
- Test event delivery and pod discovery

## Debugging

### Controller Logs

View controller logs:
```bash
kubectl logs -n knative-event-broadcaster-system deployment/knative-event-broadcaster-controller-manager
```

### Resource Status

Check BroadcastTrigger status:
```bash
kubectl get broadcasttriggers -o yaml
```

### Events

Check Kubernetes events:
```bash
kubectl get events --sort-by='.lastTimestamp'
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Update documentation
6. Submit a pull request

### Code Style

- Follow Go conventions
- Use meaningful variable names
- Add comments for complex logic
- Keep functions small and focused

### Commit Messages

Use conventional commit format:
```
feat: add support for custom event headers
fix: resolve pod discovery race condition
docs: update installation guide
test: add unit tests for event distributor
```

## Release Process

1. Update version in relevant files
2. Create release notes
3. Tag the release
4. Build and push container images
5. Update documentation
