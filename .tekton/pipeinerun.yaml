apiVersion: tekton.dev/v1beta1
kind: PipelineRun
metadata:
  name: harbor-robot-gen
  annotations:
    pipelinesascode.tekton.dev/on-comment: "(/test-all)"
    pipelinesascode.tekton.dev/on-cel-expression: |-
      (target_branch == "main" || target_branch.startsWith("release-") || event == "pull_request" )
    pipelinesascode.tekton.dev/pipeline: "[.tekton/pipelines/pipeline.yaml]"
    pipelinesascode.tekton.dev/task: "[.tekton/tasks/calculate-revision.yaml, .tekton/tasks/write-git-revision.yaml]"
    pipelinesascode.tekton.dev/max-keep-runs: "10"
spec:
  taskRunTemplate:
    podTemplate:
      securityContext:
        runAsUser: 65532
        runAsGroup: 65532
        fsGroup: 65532
        fsGroupChangePolicy: "OnRootMismatch"
  taskRunSpecs:
  - pipelineTaskName: build-dist
    computeResources:
      limits:
        cpu: "4"
        memory: 4Gi
  - pipelineTaskName: govulncheck
    computeResources:
      limits:
        cpu: "4"
        memory: "4Gi"
      requests:
        cpu: "2"
        memory: "2Gi"
  timeouts:
    pipeline: "1h"
  pipelineRef:
    name: harbor-robot-gen
  params:
  - name: git-revision
    value:
      url: "{{ repo_url }}"
      branch: "{{ source_branch }}"
      commit: "{{ revision }}"
      pull-request-source: "{{ source_branch }}"
      pull-request-target: "{{ target_branch }}"
      pull-request-number: "{{ pull_request_number }}"
  - name: base-image
    value: "{{ image-distroless }}"
  workspaces:
  - name: source
    volumeClaimTemplate:
      spec:
        accessModes:
        - ReadWriteMany
        resources:
          requests:
            storage: 200Mi
  - name: operator-source
    volumeClaimTemplate:
      spec:
        accessModes:
        - ReadWriteMany
        resources:
          requests:
            storage: 200Mi
  - name: cache
    persistentVolumeClaim:
      claimName: build-cache
    subPath: golang
  - name: basic-auth
    secret:
      secretName: github-credentials
