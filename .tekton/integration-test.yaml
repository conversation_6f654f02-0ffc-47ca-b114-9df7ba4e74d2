apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: harbor-robot-gen-integration-test
  annotations:
    pipelinesascode.tekton.dev/on-event: "[incoming]"
    pipelinesascode.tekton.dev/on-comment: "^((/test-all)|(/integration-test)|(/test-multi.* integration-test.*))"
    pipelinesascode.tekton.dev/max-keep-runs: "5"
spec:
  taskRunTemplate:
    podTemplate:
      securityContext:
        fsGroup: 65532
        fsGroupChangePolicy: "OnRootMismatch"
  taskRunSpecs:
  - pipelineTaskName: run-test
    computeResources:
      limits:
        cpu: "4"
        memory: 4Gi

  pipelineRef:
    resolver: hub
    params:
    - name: catalog
      value: alauda
    - name: type
      value: tekton
    - name: kind
      value: pipeline
    - name: name
      value: vcluster-integration-test
    - name: version
      value: "0.2"

  params:
  - name: git-revision
    value:
      url: "{{ repo_url }}"
      branch: "{{ source_branch }}"
      commit: "{{ revision }}"
  - name: prepare
    value:
      image: registry.alauda.cn:60080/devops/noroot/builder-go:latest
      command: |
        set -ex
        cd $(workspaces.source.path)/testing

        echo "======= Installing kubectl"
        export KUBECTL_VERSION=1.28.2
        if [ "$(arch)" = "arm64" ] || [ "$(arch)" = "aarch64" ]; then
            export ARCH="arm64"
        else
            export ARCH="amd64"
        fi
        mkdir -p ./bin
        curl -sfL https://dl.k8s.io/release/v${KUBECTL_VERSION}/bin/linux/${ARCH}/kubectl -o ./bin/kubectl
        chmod +x ./bin/kubectl

        export PATH=$(pwd)/bin:$PATH
        export KUBECONFIG=$(workspaces.custom.path)/vcluster-config
        kubectl config view

        echo "======= Installing cert-manager"
        cd $(workspaces.source.path) && make certmanager

        echo "======= Installing harbor-robot-gen manifests <{{ source_branch }}, and wait for ready"
        nexus="https://build-nexus.alauda.cn/repository/alauda"
        manifests="devops/harbor-robot-gen/install-manifests/{{ source_branch }}/install.yaml"
        kubectl apply -f ${nexus}/${manifests}
        kubectl -n harbor-robot-gen-system rollout status deploy/harbor-robot-gen-api --timeout=5m
        kubectl -n harbor-robot-gen-system rollout status deploy/harbor-robot-gen-controller-manager --timeout=5m
        kubectl -n harbor-robot-gen-system rollout status deploy/harbor-robot-gen-proxy --timeout=5m

  - name: build-test-image
    value:
      image-repository: build-harbor.alauda.cn/devops/harbor-robot-gen-test
      dockerfile-path: testing/Dockerfile

  - name: commit-test-image
    value:
      upstream: |
        repo-url: https://github.com/AlaudaDevops/devops-artifact.git
        branch-name: {{ source_branch }}
        yaml-file-path: ./values.yaml
      upstream-branch-condition: "^(main|master|release-.*|alauda-.*)$"
      upstream-secret: github-credentials

  - name: test
    value:
      command: |
        set -x
        cd $(workspaces.source.path)/testing

        echo "======= Prepare kubeconfig"
        export PATH=$(pwd)/bin:$PATH
        export KUBECONFIG=$(workspaces.config.path)/vcluster-config
        kubectl config view

        echo "======= Run integration test"
        export REPORT=allure
        export GODOG_ARGS="--godog.format=allure --godog.concurrency=1"
        make test
  - name: report
    value:
      command: |
        cd $(workspaces.source.path)/testing
        allure generate --clean

        echo "Listing contents of report directory"
        ls -al
      path: testing/allure-report
  workspaces:
  - name: kube-config
    volumeClaimTemplate:
      spec:
        accessModes:
        - ReadWriteMany
        resources:
          requests:
            storage: 50Mi
  - name: cache
    persistentVolumeClaim:
      claimName: build-cache
    subPath: golang
  - name: source
    volumeClaimTemplate:
      spec:
        accessModes:
        - ReadWriteMany
        resources:
          requests:
            storage: 200Mi
  - name: upload-conf
    secret:
      secretName: upload-allure-report-conf
  - name: dockerconfig
    secret:
      secretName: build-harbor.kauto.docfj
  - name: basic-auth
    secret:
      secretName: github-credentials
  - name: gitversion-config
    configMap:
      name: gitversion-config
