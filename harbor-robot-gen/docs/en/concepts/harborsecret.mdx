---
weight: 10
sourceSHA: ddda7f6f7ea4cb48199e7ffb7db50e058f08aa0ac0cacfa8695f6335aa380925
---

# Harbor Connection Information Configuration

To enable the cluster to access Harbor and invoke Harbor's API, we need to configure the connection information for Harbor.

We specify this information through a Secret.

The Secret type is `kubernetes.io/opaque` and mainly contains the following information:

- Marks that the current Secret will be provided for Harbor Robot Gen use
- Connection information such as address/username/password
- Fields used when automatically generating Harbor Projects

## Marking the Current Secret for Harbor Robot Gen

Mark the current Secret for Harbor Robot Gen use through the following Label in the Secret:

```yaml
metadata:
  labels:
    harbor-robot-gen: "true"
```

## Connection Information Including Address/Username/Password

The Data in the Secret must include the following fields:

- `url`: The address of Harbor, for example [https://harbor.example.com](https://harbor.example.com)
- `username`: The username required to access Harbor, e.g., user1
- `password`: The password required to access Harbor, e.g., pass1

Please note that due to the current design of Harbor, only Harbor administrators have the permission to create Projects, so the user specified here must be an administrator.

Please note that due to the issue in the current Harbor version 2.12 [where robot accounts cannot manage robot accounts](https://github.com/goharbor/harbor/issues/21922), the user here cannot be a robot account. It is advisable to create a separate user specifically for `Harbor Robot Gen`.

## Configuring the Name Used When Automatically Generating Harbor Projects

If you expect to automatically generate a Harbor Project on Harbor, you can define the name of the Harbor Project by using the `harbor-robot-gen/projectFieldsPath` annotation. Its value is a [kubectl style JSONPath template](https://kubernetes.io/docs/reference/kubectl/jsonpath/)

At runtime, it will traverse all Namespaces in the cluster and use the value of the specified JSONPath field as the name of the Harbor Project.

For example, the following indicates that the annotation named `goharbor.io/project` in the Namespace will be used as the name of the Harbor Project:

```yaml
metadata:
  annotations:
    harbor-robot-gen/projectFieldsPath: "{.metadata.annotations.goharbor\.io/project}"
```

### More Examples of Using JSONPath for Harbor Project Naming

| JSONPath | Description |
| --- | --- |
| `{.metadata.name}` | Use the Namespace name as the Harbor Project name. The Harbor Project will correspond one-to-one with the cluster Namespace. |
| `{.metadata.labels.cpaas\.io/inner-namespace}` | In ACP clusters, Namespaces with the same project will contain this label. This means the ACP project name will be used as the Harbor Project name, and all ACP projects will each have a Harbor Project with the same name. |
| `{.metadata.annotations.goharbor\.io/project}` | Use the value of the `goharbor.io/project` annotation on the Namespace as the Harbor Project name. |

> **Note:**
> Please use an appropriate JSONPath to specify the Harbor Project name. Avoid having too many Namespaces with different value, which may lead to the creation of too many Projects in Harbor. Also, the system will NOT automatically clean up Harbor Projects that were generated; you need to clean them up manually if necessary.


## Advanced Configuration

### Multiple Access Address Configuration

When your Harbor registry is accessible through multiple addresses, you may need to generate `Image Pull Secrets` for alternative endpoints when using HarborRobotBinding. This can be achieved using the `url.alias` configuration.

**Configuration Parameters:**

- `url.alias`: Alternative access address for the Harbor registry (e.g., `https://harbor.example.com` as an alias for `https://harbor-1.example.com`)
- `url.alias.policy`: Policy determining when to generate secrets for the alias address
  * `IfIPEqual`: Generate an `Image Pull Secret` for `url.alias` only when the resolved IP addresses of `url.alias` and `url` are identical
  * `Always`: Always generate an `Image Pull Secret` for `url.alias` regardless of IP resolution
- `url.alias.check.interval`: Interval for policy evaluation checks, specified in [Go duration format](https://pkg.go.dev/time#ParseDuration) (e.g., `2m`). Default: `1m`

**Behavior:**
The `Image Pull Secret` generated for `url.alias` contains identical credentials to the primary `url` secret, with only the registry address differing.

**Use Case:**
The `url.alias.policy: IfIPEqual` configuration is particularly well-suited for Harbor disaster recovery scenarios implementing DNS-based failover. For comprehensive implementation guidance, see [Harbor Disaster Recovery Scenario Usage](../howto/using_in_harbor_disaster_failover.mdx)

## Examples

**Example 1**

``` yaml
kind: Secret
apiVersion: v1
metadata:
  name: harbor
  namespace: harbor-robot-gen-system
  annotations:
    "harbor-robot-gen/projectFieldsPath": '{.metadata.annotations.goharbor\.io/project}'
  labels:
    harbor-robot-gen: "true"
type: kubernetes.io/opaque
stringData:
  username: user1
  password: pass1
  url: https://harbor-1.example.com
```

**Example 2**

``` yaml
kind: Secret
apiVersion: v1
metadata:
  name: harbor
  namespace: harbor-robot-gen-system
  annotations:
    "harbor-robot-gen/projectFieldsPath": '{.metadata.annotations.goharbor\.io/project}'
  labels:
    harbor-robot-gen: "true"
type: kubernetes.io/opaque
stringData:
  username: user1
  password: pass1
  url: https://harbor-1.example.com
  url.alias: https://harbor.example.com
  url.alias.policy: IfIPEqual # When the IP address resolved from url.alias and url are the same, generate an `Image Pull Secret` for `url.alias`. Default is IfIPEqual
  url.alias.check.interval: 2m # The interval for checking the policy, using [golang duration](https://pkg.go.dev/time#ParseDuration) format, for example `2m`. Default is 1m.
```