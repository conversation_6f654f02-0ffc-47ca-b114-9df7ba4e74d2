---
weight: 20
sourceSHA: f501295a788a5ce575fb94fcb41066b8d5da9178abe1cf04aadd764b8925efeb
---

# HarborRobotBinding

## Overview

`HarborRobotBinding` is a cluster-level resource that primarily defines the namespaces in Kubernetes for which we expect to automatically generate credentials for Harbor robot accounts. It also specifies the permissions with which these robot accounts can access the Harbor project.

- Scope of target namespaces
- Accessible Harbor projects for robot account generation and the associated permissions
- Refresh interval for the robot account credentials
- Name of the generated Kubernetes Secret and the name of the expected bound Service Account (SA)

## Expected Kubernetes Namespace Scope for Automatic Secret Generation

Specify the expected namespaces for automatically generating Kubernetes secrets through `spec.namespaces`. Typically, this scope represents namespaces with the same type of Harbor access permissions, such as all namespaces for a team.

- Specifying the scope via names

```yaml
spec:
  namespaces:
    names:
    - default
    - dev-ns1
```

- Specifying the scope via a selector; the data structure of the selector can refer to [K8S LabelSelector](https://pkg.go.dev/k8s.io/apimachinery@v0.33.0/pkg/apis/meta/v1#LabelSelector)

```yaml
spec:
  namespaces:
    selector:
      matchLabels:
        goharbor.io/project: team-1
```

When both names and selector are specified, the union will be taken. If both names and selector are empty, the target namespaces are considered empty.

## Harbor Configuration Information

### Used Harbor Connection Configuration

Specify this using `spec.harbor.secret`. The format of the secret can be referenced in [Harbor Connection Configuration](./harborsecret.mdx)

```yaml
spec:
  harbor:
    secret:
      name: harbor
      namespace: harbor-robot-gen-system
```

### Projects and Permissions for Robot Accounts

Specify under which Harbor project the robot account should be created via `spec.harbor.project` and define the robot's permissions using `spec.harbor.robot`.

```yaml
spec:
  harbor:
    project: team
    robot:
      access:
      - action: pull
        resource: repository
```

Among them, the parameter configuration for `harbor.robot.access[].action` and `harbor.robot.access[].resource` can be referenced in the Harbor official API documentation when creating robot accounts.

### Refresh Interval for Robot Account Credentials

```yaml
spec:
  refreshInterval: 6h
```

### Name of the Generated Kubernetes Secret and Expected Bound Service Account Name

Use `spec.generatedSecret` to specify the name of the generated Kubernetes Secret. Please avoid naming conflicts with existing secrets in the namespace. The type of the secret is `kubernetes.io/dockerconfigjson`. Use `spec.serviceAccount` to specify the name of the bound Service Account; if empty, only the Secret will be generated.

```yaml
apiVersion: harbor-robot-gen.alaudadevops.alauda.io/v1alpha1
kind: HarborRobotBinding
metadata:
  name: harbor-for-team
spec:
  generatedSecret:
    name: harbor-for-team.robot
  serviceAccount:
    name: default
```

## Example

The following resource will:

- Generate a robot account with only repository pull permissions under the Harbor project named `team`.
- Synchronize the credentials of the robot account to all namespaces containing the label `goharbor.io/project: team-1`, creating a secret of type dockerconfigjson.
- Attach the secret to the `imagePullSecrets` field of the default service account in the corresponding namespaces.
- Refresh the credentials every 6 hours.

```yaml
apiVersion: harbor-robot-gen.alaudadevops.alauda.io/v1alpha1
kind: HarborRobotBinding
metadata:
  name: harbor-for-team
spec:
  generatedSecret:
    name: harbor-for-team.robot
  serviceAccount:
    name: default
  namespaces:
    selector:
      matchLabels:
        goharbor.io/project: team-1

  harbor:
    project: team
    robot:
      access:
      - action: pull
        resource: repository
    secret:
      name: harbor
      namespace: harbor-robot-gen-system
  refreshInterval: 6h
```
