---
weight: 10
sourceSHA: ff0c569d8e401598f7ae485f0eaca296eaaef44fb0b7c6303ec30e5d8840f638
---

# Introduction

## What is Harbor Robot Gen

In a K8S cluster, to pull private images, we need to configure a `Secret` in the K8S cluster, bind this Secret to a Service Account (SA), or specify the `imagePullSecrets` field when creating a Workload. In enterprises, with the expansion of team sizes, the number of namespaces to be managed will increase. For different teams, we also hope that the permissions of the credentials used in each team's namespace are limited and controllable. At the same time, if the credentials are leaked, the associated risks should be small enough.

`Harbor Robot Gen` was created to address these issues.

## Compatible Harbor Versions

- Harbor >= v2.12

## Function Overview

- Automatically create corresponding Projects on Harbor based on Namespace information.
- Automatically create robot accounts on Harbor Projects, and use the credentials of the robot accounts to create K8S Secrets for image pulling in the corresponding K8S Namespace.
- Regularly refresh the credentials of the robot accounts and synchronize them to the Secrets of the Namespace.
- Attach the created Secrets to the `imagePullSecret` of the Service Account.

For more information, please refer to:

- [Quick Start](./quick_start.mdx)
