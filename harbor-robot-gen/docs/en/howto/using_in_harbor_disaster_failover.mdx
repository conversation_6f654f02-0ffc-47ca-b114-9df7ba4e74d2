---
weight: 30
i18n:
  title:
    en: Using Harbor Robot Gen in Harbor Disaster Recovery Failover
---

# Using Harbor Robot Gen in Harbor Disaster Recovery Failover

In a Harbor disaster recovery scenario, you may have two Harbor instances (primary and secondary) with DNS switching implemented to achieve Harbor failover between primary and secondary sites.

For example, consider the following architecture:

- Harbor 1 and Harbor 2 synchronize images to ensure image consistency
- harbor.example.com uses DNS to point to either Harbor 1 or Harbor 2
- Business clusters access the image registry through harbor.example.com

``` mermaid
graph BT
    Cluster[K8S Cluster<br/>&lpar;HarborRobotBinding&rpar;]
    H0[harbor.example.com<br/>&lpar;DNS Switching&rpar;]
    H1[Harbor 1 Service<br/>harbor-1.example.com]
    H2[Harbor 2 Service<br/>harbor-2.example.com]

    Cluster --> H0
    H0 --> H1
    H0 -.-> H2
```

This document describes how to use Harbor Robot Gen in clusters, ensuring that when DNS switching occurs, the cluster can use credentials generated by Harbor Robot Gen to pull images normally.

## Prerequisites

- Kubernetes Cluster
- `Harbor Robot Gen` installed in the cluster
- Harbor 1 and Harbor 2 prepared, accessible via harbor-1.example.com and harbor-2.example.com respectively
- Domain name harbor.example.com prepared and Harbor configured so that after DNS switching, both Harbor 1 and Harbor 2 can be accessed through harbor.example.com

## Overview

**Process Overview**

| No. | Operation Step | Description |
|-----|----------------|-------------|
| 1 | Prepare and Apply Harbor Secret | Prepare Harbor secret to connect to Harbor |
| 2 | Apply HarborRobotBinding Resources | Create HarborRobotBinding resources for both Harbor instances |
| 3 | Verify Image Pulling | Create Pod to verify image pulling functionality |
| 4 | Switch DNS to Verify Image Pulling | Switch DNS and verify image pulling continues to work |

**Key Configurations**

- Use the `url.alias` capability in [Harbor Secret](../concepts/harborsecret.mdx) configuration to specify access aliases for Harbor 1 and Harbor 2
- Use HarborRobotBinding resources to generate Image Pull Secrets for both Harbor 1 and Harbor 2

## Steps to Operate

### Step 1: Prepare and Apply Harbor Secret

To enable the cluster to access Harbor and invoke Harbor APIs, we need to configure the connection information for Harbor. We will specify this information through a Secret.

Prepare Harbor Secret for Harbor 1:

``` bash
cat << 'EOF' | kubectl apply -f -
kind: Secret
apiVersion: v1
metadata:
  name: harbor1
  namespace: harbor-robot-gen-system
  annotations:
    "harbor-robot-gen/projectFieldsPath": '{.metadata.labels.cpaas\.io/inner-namespace}'
  labels:
    harbor-robot-gen: "true"
type: kubernetes.io/opaque
stringData:
  username: user1
  password: pass1
  url: https://harbor-1.example.com
  url.alias: https://harbor.example.com
  url.alias.policy: IfIPEqual
EOF
```

Prepare Harbor Secret for Harbor 2:

```bash
cat << 'EOF' | kubectl apply -f -
kind: Secret
apiVersion: v1
metadata:
  name: harbor2
  namespace: harbor-robot-gen-system
  annotations:
    "harbor-robot-gen/projectFieldsPath": '{.metadata.labels.cpaas\.io/inner-namespace}'
  labels:
    harbor-robot-gen: "true"
type: kubernetes.io/opaque
stringData:
  username: user1
  password: pass1
  url: https://harbor-2.example.com
  url.alias: https://harbor.example.com
  url.alias.policy: IfIPEqual
EOF
```

We specify the `url.alias` as `https://harbor.example.com` and `url.alias.policy` as `IfIPEqual`, so `HarborRobotBinding` will generate an alias secret using `url.alias` when the resolved IP addresses are equal to those of `url`.

For more information about `url.alias` configuration, please refer to [Harbor Connection Information Configuration](../concepts/harborsecret.mdx).

### Step 2: Apply HarborRobotBinding Resources

We need to create `HarborRobotBinding` resources for both Harbor 1 and Harbor 2, enabling us to generate image pull secrets for pulling from harbor-1.example.com and harbor-2.example.com respectively. According to the configuration in Step 1, it will also generate image pull secrets for pulling from harbor.example.com, which are copied from the Harbor 1 image pull secrets.

`HarborRobotBinding` for harbor-1.example.com:

``` bash
cat << EOF | kubectl apply -f -
apiVersion: harbor-robot-gen.alaudadevops.alauda.io/v1alpha1
kind: HarborRobotBinding
metadata:
  name: harbor1-for-team1
spec:
  generatedSecret:
    name: harbor1-for-team1.robot
  serviceAccount:
    name: default
  namespaces:
    names:
    - team1-demo
  harbor:
    project: team1
    robot:
      access:
      - action: pull
        resource: repository
    secret:
      name: harbor1
      namespace: harbor-robot-gen-system
  refreshInterval: 1h
EOF
```

`HarborRobotBinding` for harbor-2.example.com:

```bash
cat << EOF | kubectl apply -f -
apiVersion: harbor-robot-gen.alaudadevops.alauda.io/v1alpha1
kind: HarborRobotBinding
metadata:
  name: harbor2-for-team1
spec:
  generatedSecret:
    name: harbor2-for-team1.robot # Note: Avoid name conflicts with the Harbor 1 HarborRobotBinding generated secret name
  serviceAccount:
    name: default
  namespaces:
    names:
    - team1-demo
  harbor:
    project: team1
    robot:
      access:
      - action: pull
        resource: repository
    secret:
      name: harbor2
      namespace: harbor-robot-gen-system
  refreshInterval: 1h
EOF
```

After applying the configurations, we can verify that both HarborRobotBinding resources have been created successfully:

```
$ kubectl get harborrobotbinding
NAME                  READY   LASTREFRESHTIME        NEXTREFRESHTIME        AGE
harbor1-for-team1     True    2025-06-19T05:28:12Z   2025-06-19T06:28:12Z   4h
harbor2-for-team1     True    2025-06-19T05:28:12Z   2025-06-19T06:28:12Z   4h
```

Wait a few seconds for the secrets to be generated:

```bash
$ kubectl get secret -n team1-demo

NAME                              TYPE                             DATA   AGE
harbor1-team1.robot             kubernetes.io/dockerconfigjson   1      4h1m
harbor1-team1.robot.alias       kubernetes.io/dockerconfigjson   1      4h
harbor2-team1.robot             kubernetes.io/dockerconfigjson   1      4h1m
```

Since harbor.example.com currently resolves to the same IP as harbor-1.example.com, the secret named `harbor1-for-team1.robot.alias` is generated.

We can verify that the secret `harbor1-for-team1.robot.alias` uses the registry address `https://harbor.example.com` while the remaining data is identical to the `harbor1-for-team1.robot` secret:

```
$ kubectl get secret -n team1-demo harbor1-for-team1.robot.alias -ojsonpath='{ .data.\.dockerconfigjson }' | base64 -d

{"auths":{"https://harbor.example.com":{"auth":"cm9ibxxxxx==","password":"sFM0Hxxxxxhwau","username":"robot$xxxxx"}}}

$ kubectl get secret -n team1-demo harbor1-for-team1.robot -ojsonpath='{ .data.\.dockerconfigjson }' | base64 -d

{"auths":{"https://harbor-1.example.com":{"auth":"cm9ibxxxxx==","password":"sFM0Hxxxxxhwau","username":"robot$xxxxx"}}}
```

The default ServiceAccount now includes the secrets `harbor1-for-team1.robot`, `harbor2-for-team1.robot`, and `harbor1-for-team1.robot.alias`:

```bash
$ kubectl get sa -n team1-demo default -oyaml

apiVersion: v1
imagePullSecrets:
- name: harbor1-for-team1.robot
- name: harbor2-for-team1.robot
- name: harbor1-for-team1.robot.alias
kind: ServiceAccount
metadata:
  name: default
  namespace: team1-demo
```

Now we can pull images from harbor-1.example.com using the `harbor1-for-team1.robot` secret, from harbor-2.example.com using the `harbor2-for-team1.robot` secret, and from harbor.example.com using the `harbor1-for-team1.robot.alias` secret. Let's verify this functionality.

### Step 3: Verify Image Pulling

Create Pods to test image pulling functionality:

**Note:** Ensure that both `harbor-1.example.com` and `harbor-2.example.com` have an image named `team1/busybox:stable`.

```bash
kubectl run demo --image-pull-policy=Always --image=harbor.example.com/team1/busybox:stable -n team1-demo -- sleep 3600
kubectl run demo1 --image-pull-policy=Always --image=harbor-1.example.com/team1/busybox:stable -n team1-demo -- sleep 3600
kubectl run demo2 --image-pull-policy=Always --image=harbor-2.example.com/team1/busybox:stable -n team1-demo -- sleep 3600
```

Verify that all pods are running successfully.

### Step 4: Switch DNS to Verify Image Pulling

Now, switch the DNS record for `harbor.example.com` to point to the IP address of `harbor-2.example.com`. Wait a few minutes (the exact time depends on your TTL settings in the DNS server), and you should see the following secrets in the namespace:

```bash
$ kubectl get secret -n team1-demo

NAME                              TYPE                             DATA   AGE
harbor1-for-team1.robot           kubernetes.io/dockerconfigjson   1      4h1m
harbor2-for-team1.robot           kubernetes.io/dockerconfigjson   1      4h1m
harbor2-for-team1.robot.alias     kubernetes.io/dockerconfigjson   1      4h
```

Verify the secret contents:

```bash
$ kubectl get secret -n team1-demo harbor2-for-team1.robot -ojsonpath='{ .data.\.dockerconfigjson }' | base64 -d

{"auths":{"https://harbor-2.example.com":{"auth":"cm9ibxxxxx==","password":"sFM0Hxxxxxhwau","username":"robot$xxxxx"}}}

$ kubectl get secret -n team1-demo harbor2-for-team1.robot.alias -ojsonpath='{ .data.\.dockerconfigjson }' | base64 -d

{"auths":{"https://harbor.example.com":{"auth":"cm9ibxxxxx==","password":"sFM0Hxxxxxhwau","username":"robot$xxxxx"}}}
```

The image pull secrets in the ServiceAccount have also changed:

```bash
$ kubectl get sa -n team1-demo default -oyaml

apiVersion: v1
imagePullSecrets:
- name: harbor1-for-team1.robot
- name: harbor2-for-team1.robot
- name: harbor2-for-team1.robot.alias
kind: ServiceAccount
metadata:
  name: default
  namespace: team1-demo
```

Now we can use `harbor2-for-team1.robot.alias` to pull images from `harbor.example.com`. Let's verify this:

```bash
kubectl run demo --image-pull-policy=Always --image=harbor.example.com/team1/busybox:stable -n team1-demo -- sleep 3600
kubectl run demo2 --image-pull-policy=Always --image=harbor-2.example.com/team1/busybox:stable -n team1-demo -- sleep 3600
```

After creating the pods, you should see all pods running successfully.

## Summary

Through the above process, we have successfully demonstrated how to use Harbor Robot Gen in a Harbor disaster recovery scenario with DNS switching to pull images from both primary and secondary Harbor instances.

This solution provides a robust foundation for Harbor disaster recovery implementations while maintaining operational simplicity.

## Related Documentation

- [Harbor Connection Information Configuration](../concepts/harborsecret.mdx)
- [Understanding HarborRobotBinding](../concepts/harborrobotbinding.mdx)