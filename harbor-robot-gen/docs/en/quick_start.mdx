---
weight: 20
sourceSHA: 795f5cb229f4e53ef5d6ef59fa8b7b345b08cd7f737270c1be3e9588558e6131
---

# Quick Start

This document will help you quickly understand and use Harbor Robot Gen to generate Harbor robot account credentials in the specified k8s namespace, allowing Pods to pull images with minimal permissions and periodically refresh these credentials.

## Estimated Reading Time

10-15 minutes

## Prerequisites

- A Kubernetes cluster with the following components installed:
  - Harbor Robot Gen
  - Three namespaces created in the cluster: `team`, `team-ns1`, `team-ns2`
  - The `team` namespace contains the label `cpaas.io/inner-namespace`, with the value `team`
- A properly functioning Harbor

## Overview of the Procedure

| No | Procedure                      | Description                                        |
| -- | ------------------------------ | -------------------------------------------------- |
| 1  | Configure Harbor Address Information   | Configure the Harbor address, username, password, etc.                 |
| 2  | Create HarborRobotBinding Resource | Create the configuration to synchronize the robot account               |
| 3  | Verify the Result              | Automatically create the Project in Harbor and verify that the robot account and credentials are as expected |

## Steps

### Step 1: Configure Harbor Connection Information

In order for the cluster to access Harbor and call the Harbor API, we need to configure the connection information for Harbor. We will specify this information through a Secret.

```
cat << 'EOF' | kubectl apply -f -
kind: Secret
apiVersion: v1
metadata:
  name: harbor
  namespace: harbor-robot-gen-system
  annotations:
    # When automatically creating Harbor Project, use the label: cpaas.io/inner-namespace on the namespace as the name of the Harbor Project
    harbor-robot-gen/projectFieldsPath: "{.metadata.labels.cpaas\.io/inner-namespace}"
  labels:
    harbor-robot-gen: "true" # Mark this Secret as available for Harbor Robot Gen
type: kubernetes.io/opaque
stringData:
  username: api-user # Username used to call the Harbor API
  password: api-password # Password used to call the Harbor API
  url: https://harbor.example.com # The address of Harbor
EOF
```

Due to the current design of Harbor, only Harbor administrators have the permission to create Projects, so the user specified here must be an administrator.

For more information on configuring Harbor connection information, please refer to [Harbor Connection Information Configuration](./concepts/harborsecret.mdx).

### Step 2: Create HarborRobotBinding Resource

Assuming we want the k8s namespaces named `team-ns1` and `team-ns2` to access the Harbor Project named `team` in Harbor. (Make sure the k8s namespaces are created in advance)

We need to create the following `HarborRobotBinding` resource:

```bash
cat << EOF | kubectl apply -f -
apiVersion: harbor-robot-gen.alaudadevops.alauda.io/v1alpha1
kind: HarborRobotBinding
metadata:
  name: harbor-secret-for-team
spec:
  # The k8s namespaces where the secret will be created
  namespaces:
    names:
    - team-ns1
    - team-ns2
  generatedSecret:
    name: harbor-secret-for-team.robot  # The name of the Secret generated in the cluster
  serviceAccount:
    name: default # Automatically bind the secret to the imagePullSecrets field of the default service account

  harbor:
    project: team # The expected name of the project in Harbor to access
    robot:
      access: # Permissions of the automatically created Robot
      - action: pull
        resource: repository
    secret: # Harbor configuration information, as configured in Step 1
      name: harbor
      namespace: harbor-robot-gen-system

  refreshInterval: 6h # Refresh time for Robot credentials
EOF
```

For more information about HarborRobotBinding configuration, please refer to [HarborRobotBinding Configuration](../concepts/harborrobotbinding.mdx).

### Step 3: Verify the Result

1. Check if the Harbor Project named `team` was automatically generated in Harbor.

2. Check if a robot account named `robot$team+harbor-secret-for-team-xxxx` was generated under the `team` project in Harbor.

3. Check if the status of HarborRobotBinding is Ready=True

```bash
$ kubectl get HarborRobotBinding -A

NAME                     READY   LASTREFRESHTIME        NEXTREFRESHTIME        AGE
harbor-secret-for-team   True    2025-05-15T10:33:41Z   2025-05-15T16:33:41Z   20h
```

4. Check if a Secret named `harbor-secret-for-team.robot` was created in the target namespace

```bash
$ kubectl get secret -n <namespace>

NAME                                   TYPE                             DATA   AGE
harbor-secret-for-team.robot           kubernetes.io/dockerconfigjson   1      20h
```

5. Check whether the imagePullSecrets of the service account in the target namespace automatically includes the above Secret

```bash
$ kubectl get sa default -n <namespace> -o yaml

apiVersion: v1
imagePullSecrets:
- name: harbor-secret-for-team.robot
kind: ServiceAccount
metadata:
  name: default
```

6. Create a Pod in the target namespace and confirm whether it can pull the image normally.

```bash
cat << EOF | kubectl apply -f -
kind: Pod
apiVersion: v1
metadata:
  name: test-pod
  namespace: <namespace>
spec:
  containers:
  - name: test
    image: <your-image-address-in-team-project>
    imagePullPolicy: Always
    command: ["sleep"]
    args: ["3600"]
EOF
```

At this point, you have successfully used `Harbor Robot Gen` to automatically create the team Project on Harbor and generate a K8S Secret with only `pull repository` permission, which can access the Harbor Project named `team` in the `team-ns1` and `team-ns2` namespaces. Additionally, this credential will be automatically refreshed every 6 hours.

## How It Works

- `Harbor Robot Gen` checks for credentials in the system that have the label `harbor-robot-gen: "true"`, reading the value of the annotation `harbor-robot-gen/projectFieldsPath` configured on the credential. This value is a JsonPath. When traversing the current cluster's k8s namespaces, it uses this JsonPath to access the specific field values on the namespace as the names for the Harbor Projects to be created, completing the creation of the Harbor Projects in Harbor.
- After creating the `HarborRobotBinding`, `Harbor Robot Gen` will create robot accounts on the specified Harbor Project. Simultaneously, within the specified k8s namespace scope, it creates k8s Secrets using the credentials of the robot account and binds them to the specified service account. The credentials of the robot account will be periodically refreshed according to the specified refresh cycle, and the latest credentials will be synchronized to k8s.

## More Information

- [Harbor Connection Information Configuration](./concepts/harborsecret.mdx)
- [Configuration to Automatically Generate Harbor Projects](./concepts/harborsecret.mdx#auto_gen_harbor_project_configuration)
- [HarborRobotBinding Configuration](./concepts/harborrobotbinding.mdx)
- [Using In Harbor Disaster Recovery Failover](./howto/using_in_harbor_disaster_failover.mdx)
