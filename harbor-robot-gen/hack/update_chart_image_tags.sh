#!/bin/bash

# Script to extract image tags from install.yaml files and update values.yaml
# Author: <PERSON>audaDevops

set -e

# Base directory
BASE_DIR=$(cd "$(dirname "$0")/.." && pwd)

# Define yq version and path
if [ -z "$YQ" ]; then
  YQ=yq4
  echo "using yq4"
fi

# Values file to update
VALUES_FILE="${BASE_DIR}/chart/values.yaml"

# Function to extract all images from all YAML files
# Returns: List of valid images (one per line)
extract_images() {

  local yaml_files=(
    "${BASE_DIR}/dist/install.yaml"
  )

  for yaml_file in "${yaml_files[@]}"; do

    $YQ 'select(.kind == "Deployment" or .kind == "DaemonSet" or .kind == "StatefulSet" or .kind == "Job") |
        .. | select(has("image")).image' "$yaml_file" | \
    grep -v '^---$' | \
    grep -v '^null$' | \
    grep -E '.+/.+:.+'
  done
}

echo "Extracting images from YAML files and updating values.yaml..."

# Get all images from all YAML files
IMAGES=$(extract_images)

# Process each image
echo "$IMAGES" | while IFS= read -r image_path; do
  # Skip empty lines
  [ -z "$image_path" ] && continue
  
  registry=$(echo "$image_path" | cut -d'/' -f1)
  repo=$(echo "$image_path" | sed -E "s|^$registry/||" | sed -E 's|:[^:]+$||')
  tag=$(echo "$image_path" | sed -E 's|.*:||')
  
  # echo "image: $image_path"

  # Find matching component in values.yaml directly with yq
  component=$($YQ -r ".global.images | to_entries | .[] | select(.value.repository == \"$repo\") | .key" "$VALUES_FILE")

  if [ -n "$component" ] && [ "$component" != "null" ]; then
    # Update the tag in values.yaml directly
    echo "set .global.images.$component.tag=$tag"
    $YQ -i ".global.images.\"$component\".tag = \"$tag\"" "$VALUES_FILE"
  else
    echo "ERROR: No matching component found in values.yaml for repository: $repo"
    exit 1
  fi
done

echo "Done! Updated values.yaml with image tags."
