#!/bin/bash

# Script to extract CRD resources
# Usage: ./extract_crds.sh <source YAML file path> <target directory path>
# ./hack/extract_crds.sh cmd/kodata/connectorscore/1.0.0/install.yaml config/crd/bases

set -e

# Check parameters
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <source YAML file path> <target directory path>"
    echo "Example: $0 cmd/kodata/connectorscore/1.0.0/install.yaml config/crd/bases"
    exit 1
fi

SOURCE_FILE="$1"
TARGET_DIR="$2"

# Ensure target directory exists
mkdir -p "$TARGET_DIR"

# Create temporary directory
TEMP_DIR=$(mktemp -d)

# Use awk to split YAML file - more general approach
awk 'BEGIN { count=0; doc=""; } 
/^---$/ { 
  if (length(doc) > 0) { 
    filename=sprintf("'$TEMP_DIR'/part-%03d.yaml", count++); 
    print doc > filename; 
    doc=""; 
  } 
  next; 
} 
{ doc=doc $0 "\n"; } 
END { 
  if (length(doc) > 0) { 
    filename=sprintf("'$TEMP_DIR'/part-%03d.yaml", count++); 
    print doc > filename; 
  } 
}' "$SOURCE_FILE"

# Iterate through all split files
for part in "$TEMP_DIR"/*.yaml; do
    # Check if file exists (prevent wildcard mismatch)
    if [ ! -f "$part" ]; then
        continue
    fi
    
    # Check if it's a CRD
    if grep -q "kind: CustomResourceDefinition" "$part"; then
        
        # Extract group
        GROUP=$(grep -A 10 "spec:" "$part" | grep "group:" | head -1 | awk '{print $2}')
        
        # Extract plural
        PLURAL=$(grep -A 20 "names:" "$part" | grep "plural:" | head -1 | awk '{print $2}')
        
        if [ -n "$GROUP" ] && [ -n "$PLURAL" ]; then
            
            # Create target filename
            TARGET_FILE="${TARGET_DIR}/${GROUP}_${PLURAL}.yaml"
            
            # Copy file
            cp "$part" "$TARGET_FILE"
            echo "Synced CRD $TARGET_FILE"
        else
            echo "Could not extract group or plural from file: $part"
        fi
    fi
done

# Clean up temporary files
rm -rf "$TEMP_DIR"

echo "Done! CRDs have been extracted to directory: $TARGET_DIR"
