# Code generated by tool. DO NOT EDIT.
# This file is used to track the info used to scaffold your project
# and allow the plugins properly work.
# More info: https://book.kubebuilder.io/reference/project-config.html
domain: alauda.io
layout:
- go.kubebuilder.io/v4
multigroup: true
plugins:
  grafana.kubebuilder.io/v1-alpha: {}
projectName: harbor-robot-gen
repo: github.com/AlaudaDevops/experimental/harbor-robot-gen
resources:
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: alauda.io
  group: alaudadevops
  kind: HarborRobotBinding
  path: github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/apis/v1alpha1
  version: v1alpha1
  webhooks:
    defaulting: true
    validation: true
    webhookVersion: v1
version: "3"
