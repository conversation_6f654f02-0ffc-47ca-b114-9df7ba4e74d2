# Development

This doc explains how to setup a development environment so you can get started
[contributing](./CONTRIBUTING.md) to `Harbor Robot Account`.
Also take a look at:

- [How to add and run tests](./test/README.md)
- [Iterating](#iterating)

## Getting started

1. [Create and checkout a repo fork](#checkout-your-fork)
1. [Install](#install)

Once you meet these requirements, you can
[deploy integrations](#deploy-integrations).

Before submitting a PR, see also [contribution guidelines](./CONTRIBUTING.md).

### Requirements

You must install these tools:

1. [`go`](https://golang.org/doc/install): The language `Harbor Robot Account` is
   developed with (version 1.22 or higher)
1. [`git`](https://help.github.com/articles/set-up-git/): For source control
1. [`kubebuilder 4.2.0`](https://github.com/kubernetes-sigs/kubebuilder): For scafolding resources and webhooks.
1. [`kubectl`](https://kubernetes.io/docs/tasks/tools/install-kubectl/): For
   managing development environments.
1. [`bash`](https://www.gnu.org/software/bash/) v4 or higher. On macOS the
   default bash is too old, you can use [Homebrew](https://brew.sh) to install a
   later version. For running some automations, such as dependencies updates and
   code generators.

### Create a cluster and a repo

1. Set up a kubernetes cluster
   - kind
   - Docker for Mac/Windows
   - minikube
   - microk8s
   - k3s
   - vcluster
1. Set up a Linux Container repository for pushing images. You can use any
   container image registry by adjusting the authentication methods and
   repository paths mentioned in the sections below.
   - [Google Container Registry quickstart](https://cloud.google.com/container-registry/docs/pushing-and-pulling)
   - [Docker Hub quickstart](https://docs.docker.com/docker-hub/)

> :information_source: You'll need to be authenticated with your
> `KO_DOCKER_REPO` before pushing images. Run `gcloud auth configure-docker` if
> you are using Google Container Registry or `docker login` if you are using
> Docker Hub.

### Setup your environment

To start your environment you'll need to set these environment variables (we
recommend adding them to your `.bashrc`):

1. `GOPATH`: If you don't have one, simply pick a directory and add
   `export GOPATH=...`
1. `$GOPATH/bin` on `PATH`: This is so that tooling installed via `go get` will
   work properly.
1. `KO_DOCKER_REPO`: The docker repository to which developer images should be
   pushed (e.g. `gcr.io/[gcloud-project]`).

1. `GOPRIVATE`: Currently `github.com/AlaudaDevops/experimental/harbor-robot-gen` is private, needs to set
    GOPRIVATE=github.com/AlaudaDevops/experimental/harbor-robot-gen,

> :information_source: If you are using Docker Hub to store your images, your
> `KO_DOCKER_REPO` variable should have the format `docker.io/<username>`.
> Currently, Docker Hub doesn't let you create subdirs under your username (e.g.
> `<username>/knative`).

`.bashrc` example:

```shell
export GOPATH="$HOME/go"
export PATH="${PATH}:${GOPATH}/bin"
export KO_DOCKER_REPO='gcr.io/my-gcloud-project-id'
```

### Checkout your fork

To check out this repository:

1. Create your own
   [fork of this repo](https://help.github.com/articles/fork-a-repo/)
1. Clone it to your machine:

```shell
<NAME_EMAIL>:${YOUR_GITHUB_USERNAME}/experimental/harbor-robot-gen.git
cd harbor-robot-gen
git remote add upstream https://github.com/AlaudaDevops/experimental/harbor-robot-gen.git
git remote set-url --push upstream no_push
```

_Adding the `upstream` remote sets you up nicely for regularly
[syncing your fork](https://help.github.com/articles/syncing-a-fork/)._

Once you reach this point you are ready to do a full build and deploy as
follows.

## Deploy harbor-robot-gen

Once you've [setup your development environment](#getting-started), stand up
`harbor-robot-gen` with:

```shell
## make help for more details
make deploy

## for LOCAL deployment with Docker for Mac or windows
make deploy LOCAL=-L
```

You can see things running with:

```shell
$ kubectl -n harbor-robot-gen-system get pods
NAME                                               READY     STATUS    RESTARTS   AGE
harbor-robot-gen-controller-manager-648855cf5-759tv   1/1     Running   0          12h

```
if your cluster do not install certmanager , you should deploy it firstly

```shell
make certmanager
```

## Iterating

As you make changes to the code-base, there are two special cases to be aware
of:

- **If you change a type definition ([pkg/apis/](./pkg/apis/.)),** then you must
  run `make manifests && make generate`.
- **If you change a package's deps** (including adding external dep), then you
  must run `go mod tidy && make test`

These are both idempotent, and we expect that running these at `HEAD` to have no
diffs.

Once the codegen and dependency information is correct, redeploying the
controller is simply:

```shell
make deploy
```

Or you can [clean it up completely](#clean-up) and start again.

## Tests

Running tests as you make changes to the code-base is pretty simple. See
[the test docs](./test/README.md).

## Contributing

Please check [contribution guidelines](./CONTRIBUTING.md).

## Clean up

You can delete `harbor-robot-gen` with:

```shell
make undeploy
```

## Telemetry

WIP

