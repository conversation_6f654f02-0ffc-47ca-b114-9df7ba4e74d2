REPORT ?= pretty
GODOG_ARGS ?= --godog.concurrency=1 --godog.format=$(REPORT)
E2E_CONFIG ?= ./config.yaml
TEST_COMMAND ?= go test -timeout=1h -v -count 1 .
ARCH ?= amd64
BIN ?= ./bin

build:
	GOOS=linux GOARCH=$(ARCH) go test -c -o "$(BIN)/harbor-robot-gen.test" ./

TAGS ?= harbor-robot-gen
test: clean
	$(TEST_COMMAND) $(GODOG_ARGS) --godog.tags=$(TAGS)

report:
	allure generate --clean
	allure open

clean:
	kubectl delete --ignore-not-found=true namespace -l testing.suite-name=harbor-robot-gen
	kubectl delete --ignore-not-found=true connectorclass -l testing.suite-name=harbor-robot-gen
	kubectl delete --ignore-not-found=true clusterrolebinding -l testing.suite-name=harbor-robot-gen
	kubectl delete --ignore-not-found=true clusterrole -l testing.suite-name=harbor-robot-gen

help:
	go test -v . --godog.help
