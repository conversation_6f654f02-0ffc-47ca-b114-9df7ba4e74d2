# if you want disabled the DTD verification for a proxy problem for example, true by default
sonar.coverage.dtdVerification=false
sonar.sourceEncoding=UTF-8

sonar.sources=.
sonar.exclusions=vendor/**,test/**,**/*_generated*go,**/generated.pb.go,**/*.md,bin/*,testbin/*,**/mock/**,go.mod,go.sum,testing/**

sonar.tests=.
sonar.test.inclusions=**/*_test.go

sonar.go.tests.reportPaths=test.json
sonar.go.coverage.reportPaths=cover.out
sonar.go.golangci-lint.reportPaths=golangci-lint-report.txt
