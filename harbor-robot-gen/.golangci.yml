###################################################
### WARNING: This file is synced from katanomi/hack
### DO NOT CHANGE IT MANUALLY
###################################################
run:
  timeout: 20m

  build-tags: [ containers_image_openpgp ]

  skip-dirs:
  - config
  - test

  skip-files:
  - ".*/go/pkg/mod/.*"

linters:
  disable-all: true
  enable:
  - asciicheck
  - gosec
  - prealloc
  - unconvert
  - unparam
  #    - whitespace
  #    - govet
  - stylecheck
  - revive
  - prealloc
  - nestif
#    - gocritic
#    - forcetypeassert
#    - exportloopref
#    - durationcheck
#    - contextcheck
#    - bodyclose

linters-settings:
  nestif:
    min-complexity: 12
  prealloc:
    range-loops: true
    for-loops: true
  govet:
    check-shadowing: true
    enable-all: true
    disable:
    - fieldalignment
  revive:
    rules:
    - name: var-declaration
      disabled: true
  stylecheck:
    go: "1.15"
    checks: [ "ST1000", "ST1019", "ST1020", "ST1021", "ST1022" ]
    http-status-code-allowlist: [ "200", "400", "404", "500" ]

  gosec:
    includes:
    - G101
    # - G102
    # - G103
    # - G104
    # - G106
    # - G107
    # - G108
    # - G109
    # - G110
    # - G111
    # - G201
    # - G202
    - G203
    # - G204
    # - G301
    # - G302
    # - G303
    # - G304
    # - G305
    # - G306
    # - G307
    - G401
    # - G402
    # - G403
    # - G404
    # - G501
    # - G502
    # - G503
    # - G504
    # - G505
    # - G601

issues:
  exclude-rules:
  - path: test # Excludes /test, *_test.go etc.
    linters:
    - gosec
    - unparam
  include:
  - EXC0011 # enable comment on exported check
