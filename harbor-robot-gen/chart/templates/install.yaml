apiVersion: v1
kind: Namespace
metadata:
  labels:
    app.kubernetes.io/name: harbor-robot-gen
    control-plane: controller-manager
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-system
  annotations:
    helm.sh/resource-policy: keep
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/name: harbor-robot-gen
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-controller-manager
  namespace: harbor-robot-gen-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    app.kubernetes.io/name: harbor-robot-gen
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-leader-election-role
  namespace: harbor-robot-gen-system
rules:
  - apiGroups:
      - ""
    resources:
      - configmaps
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-manager-role
rules:
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - ""
    resources:
      - namespaces
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - secrets
      - serviceaccounts
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - harbor-robot-gen.alaudadevops.alauda.io
    resources:
      - harborrobotbindings
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - harbor-robot-gen.alaudadevops.alauda.io
    resources:
      - harborrobotbindings/finalizers
    verbs:
      - update
  - apiGroups:
      - harbor-robot-gen.alaudadevops.alauda.io
    resources:
      - harborrobotbindings/status
    verbs:
      - get
      - patch
      - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-metrics-auth-role
rules:
  - apiGroups:
      - authentication.k8s.io
    resources:
      - tokenreviews
    verbs:
      - create
  - apiGroups:
      - authorization.k8s.io
    resources:
      - subjectaccessreviews
    verbs:
      - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-metrics-reader
rules:
  - nonResourceURLs:
      - /metrics
    verbs:
      - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/name: harbor-robot-gen
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-leader-election-rolebinding
  namespace: harbor-robot-gen-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: harbor-robot-gen-leader-election-role
subjects:
  - kind: ServiceAccount
    name: harbor-robot-gen-controller-manager
    namespace: harbor-robot-gen-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/name: harbor-robot-gen
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-manager-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: harbor-robot-gen-manager-role
subjects:
  - kind: ServiceAccount
    name: harbor-robot-gen-controller-manager
    namespace: harbor-robot-gen-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-metrics-auth-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: harbor-robot-gen-metrics-auth-role
subjects:
  - kind: ServiceAccount
    name: harbor-robot-gen-controller-manager
    namespace: harbor-robot-gen-system
---
apiVersion: v1
data: {}
kind: ConfigMap
metadata:
  labels:
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-config
  namespace: harbor-robot-gen-system
---
apiVersion: v1
data:
  loglevel.harbor-robot-gen: info
  loglevel.harbor-robot-gen.harborrobotbinding-controller: info
  zap-logger-config: |
    {
      "level": "info",
      "development": false,
      "outputPaths": ["stdout"],
      "errorOutputPaths": ["stderr"],
      "encoding": "json",
      "encoderConfig": {
        "timeKey": "ts",
        "levelKey": "level",
        "nameKey": "logger",
        "callerKey": "caller",
        "messageKey": "msg",
        "stacktraceKey": "stacktrace",
        "lineEnding": "",
        "levelEncoder": "",
        "timeEncoder": "iso8601",
        "durationEncoder": "",
        "callerEncoder": ""
      }
    }
kind: ConfigMap
metadata:
  labels:
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-config-logging
  namespace: harbor-robot-gen-system
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/name: harbor-robot-gen
    control-plane: controller-manager
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-controller-manager-metrics-service
  namespace: harbor-robot-gen-system
spec:
  ports:
    - name: https
      port: 8443
      protocol: TCP
      targetPort: 8443
  selector:
    control-plane: controller-manager
    group: harbor-robot-gen.alaudadevops.alauda.io
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/name: harbor-robot-gen
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-webhook-service
  namespace: harbor-robot-gen-system
spec:
  ports:
    - port: 443
      protocol: TCP
      targetPort: 9443
  selector:
    control-plane: controller-manager
    group: harbor-robot-gen.alaudadevops.alauda.io
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: harbor-robot-gen
    control-plane: controller-manager
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-controller-manager
  namespace: harbor-robot-gen-system
spec:
  replicas: 1
  selector:
    matchLabels:
      control-plane: controller-manager
      group: harbor-robot-gen.alaudadevops.alauda.io
      service_name: harbor-robot-gen
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
        group: harbor-robot-gen.alaudadevops.alauda.io
        service_name: harbor-robot-gen
        version-hash: sha1-{{ .Chart.Version | sha1sum }}
    spec:
      containers:
        - args:
            - --leader-elect
            - --health-probe-bind-address=:8081
            - --metrics-bind-address=:8443
          env:
            - name: SYSTEM_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: CONFIG_LOGGING_NAME
              value: harbor-robot-gen-config-logging
            - name: CONFIG_NAME
              value: harbor-robot-gen-config
            - name: CLUSTER_DOMAIN
              value: cluster.local
          image: '{{ template "plugin.image" . }}'
          imagePullPolicy: '{{ template "plugin.imagePolicy" . }}'
          livenessProbe:
            httpGet:
              path: /healthz
              port: 8081
            initialDelaySeconds: 15
            periodSeconds: 20
          name: manager
          ports:
            - containerPort: 9443
              name: webhook-server
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /readyz
              port: 8081
            initialDelaySeconds: 5
            periodSeconds: 5
          resources:
            limits:
              cpu: 500m
              memory: 500Mi
            requests:
              cpu: 100m
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
          volumeMounts:
            - mountPath: /tmp/k8s-webhook-server/serving-certs
              name: cert
              readOnly: true
      securityContext:
        runAsNonRoot: true
      serviceAccountName: harbor-robot-gen-controller-manager
      terminationGracePeriodSeconds: 10
      volumes:
        - name: cert
          secret:
            defaultMode: 420
            secretName: webhook-server-cert
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  labels:
    app.kubernetes.io/component: certificate
    app.kubernetes.io/created-by: harbor-robot-gen
    app.kubernetes.io/instance: serving-cert
    app.kubernetes.io/name: certificate
    app.kubernetes.io/part-of: harbor-robot-gen
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-serving-cert
  namespace: harbor-robot-gen-system
spec:
  dnsNames:
    - harbor-robot-gen-webhook-service.harbor-robot-gen-system.svc
    - harbor-robot-gen-webhook-service.harbor-robot-gen-system.svc.cluster.local
  issuerRef:
    kind: Issuer
    name: harbor-robot-gen-selfsigned-issuer
  secretName: webhook-server-cert
---
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  labels:
    app.kubernetes.io/name: harbor-robot-gen
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-selfsigned-issuer
  namespace: harbor-robot-gen-system
spec:
  selfSigned: {}
---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  annotations:
    cert-manager.io/inject-ca-from: harbor-robot-gen-system/harbor-robot-gen-serving-cert
  labels:
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-mutating-webhook-configuration
webhooks:
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: harbor-robot-gen-webhook-service
        namespace: harbor-robot-gen-system
        path: /mutate-connectors-alauda-io-v1alpha1-connector
    failurePolicy: Fail
    name: mconnector.kb.io
    rules:
      - apiGroups:
          - harbor-robot-gen.alaudadevops.alauda.io
        apiVersions:
          - v1alpha1
        operations:
          - CREATE
          - UPDATE
        resources:
          - connectors
    sideEffects: None
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: harbor-robot-gen-webhook-service
        namespace: harbor-robot-gen-system
        path: /mutate-connectors-alauda-io-v1alpha1-connectorclass
    failurePolicy: Fail
    name: mconnectorclass.kb.io
    rules:
      - apiGroups:
          - harbor-robot-gen.alaudadevops.alauda.io
        apiVersions:
          - v1alpha1
        operations:
          - CREATE
          - UPDATE
        resources:
          - connectorclasses
    sideEffects: None
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  annotations:
    cert-manager.io/inject-ca-from: harbor-robot-gen-system/harbor-robot-gen-serving-cert
  labels:
    group: harbor-robot-gen.alaudadevops.alauda.io
  name: harbor-robot-gen-validating-webhook-configuration
webhooks:
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: harbor-robot-gen-webhook-service
        namespace: harbor-robot-gen-system
        path: /validate-connectors-alauda-io-v1alpha1-connector
    failurePolicy: Fail
    name: vconnector.kb.io
    rules:
      - apiGroups:
          - harbor-robot-gen.alaudadevops.alauda.io
        apiVersions:
          - v1alpha1
        operations:
          - CREATE
          - UPDATE
        resources:
          - connectors
    sideEffects: None
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: harbor-robot-gen-webhook-service
        namespace: harbor-robot-gen-system
        path: /validate-connectors-alauda-io-v1alpha1-connectorclass
    failurePolicy: Fail
    name: vconnectorclass.kb.io
    rules:
      - apiGroups:
          - harbor-robot-gen.alaudadevops.alauda.io
        apiVersions:
          - v1alpha1
        operations:
          - CREATE
          - UPDATE
        resources:
          - connectorclasses
    sideEffects: None
---

