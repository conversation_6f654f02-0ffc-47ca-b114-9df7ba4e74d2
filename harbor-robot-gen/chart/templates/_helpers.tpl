{{- define "plugin.image" -}}
{{- $registryAddress :=  .Values.global.registry.address -}}
{{- $repositoryName := .Values.global.images.controller.repository -}}
{{- $tag := .Values.global.images.controller.tag -}}
{{- printf "%s/%s:%s" $registryAddress $repositoryName $tag -}}
{{- end -}}

{{- define "plugin.imagePolicy" -}}
{{- printf "Always" -}}
{{- end -}}

{{- define "plugin.tolerations" -}}
tolerations:
- effect: NoSchedule
  key: no-cpaas-pod
  operator: Equal
  value: "true"
- effect: NoSchedule
  key: node-role.kubernetes.io/master
  operator: Exists
- effect: NoSchedule
  key: node-role.kubernetes.io/control-plane
  operator: Exists
- effect: NoSchedule
  key: node-role.kubernetes.io/cpaas-system
  operator: Exists
{{- end -}}
