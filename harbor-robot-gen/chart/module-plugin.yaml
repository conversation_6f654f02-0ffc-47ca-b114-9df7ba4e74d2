apiVersion: cluster.alauda.io/v1alpha1
kind: ModulePlugin
metadata:
  annotations:
    cpaas.io/built-in-plugin: "false"
    cpaas.io/display-name: '{"en": "Harbor Robot Account Generator", "zh": "Harbor Robot Account Generator"}'
    cpaas.io/module-name: harbor-robot-gen
  labels:
    cpaas.io/auto-install: "false"
    cpaas.io/product: 'ACP'
    cpaas.io/module-catalog: "harbor-robot-gen"
    cpaas.io/module-type: 'plugin'
    cpaas.io/module-name: 'harbor-robot-gen'
  name: harbor-robot-gen
spec:
  affinity:
    clusterAffinity:
  appReleases:
  - chartVersions:
    - name: devops/chart-harbor-robot-gen
      releaseName: harbor-robot-gen
      version: v0.0.1-2
    name: harbor-robot-gen
  deleteable: true
  logo: ""
  description:
    en: "Harbor Robot Account Generator will generate Harbor Robot Account, and in k8s cluster, use robot's token to create refreshable secret, and bind it to specified sa "
    zh: "Harbor Robot Account Generator 将会自动在 Harbor 上生成机器人账号，并在 k8s 集群中，使用机器人 token 创建定期刷新的 secret, 绑定到指定的 sa 上。"
  labelCluster: "false"
  mainChart: devops/chart-harbor-robot-gen
  name: harbor-robot-gen
  upgradeRiskLevel: low
  upgradeRiskDescription: "No impact on running applications."
  upgradeRiskDescriptionEn: "No impact on running applications."
  deleteRiskDescription: "Support uninstallation."
  deleteRiskDescriptionEn: "Support uninstallation."
