plugin: harbor-robot-gen
global:
  # replicas: number of replicas for stateless components
  replicas: 1
  # logLevel: a basic log level parameter
  logLevel: debug
  globalTLSSecretName: ""
  # deployment namespace
  namespace: harbor-robot-gen-system
  registry:
    # address: registry address
    address: registry.alauda.cn:60070
    imagePullSecrets: []
  # all the images and components used
  images:
    controller:
      # repository: image repository for the image
      repository: devops/harbor-robot-gen/controller
      # tag: a tag for the component
      tag: "dev-jtcheng0616-gmail.com-794d256"
  resources:
    requests:
      memory: 50Mi
      cpu: 50m
    limits:
      memory: 512Mi
      cpu: 500m
