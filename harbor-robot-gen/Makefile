include base.mk

# Component name inside cmd/
C ?= controller
# Full image address for docker build
IMG ?= build-harbor.alauda.cn/test/-$(C)
MIRRORS-IMG ?= registry.alauda.cn:60080/test/-$(C)
# Tag to be used in the docker image
TAG ?= dev-$(shell git config --get user.email | sed -e "s/@/-/")-$(shell git rev-parse --short HEAD)
CHART_VERSION ?= $(TAG)

# used for ko build
PLATFORM ?= linux/amd64
COMMIT ?= $(shell git rev-parse HEAD)
BRANCH ?= $(shell git rev-parse --abbrev-ref HEAD)
CRD_DOCS_OUTPUT_PATH = ./docs/references/crds
KO_OPTS ?= --tag-only

##@ Development

.PHONY: manifests
manifests: controller-gen ## Generate WebhookConfiguration, ClusterRole and CustomResourceDefinition objects.
	$(CONTROLLER_GEN) crd webhook paths="./..." output:crd:artifacts:config=config/crd/bases
	$(CONTROLLER_GEN) rbac:roleName=manager-role paths="./cmd/controller" output:rbac:artifacts:config=config/rbac/manager

deploy: dist certmanager
	kubectl apply -f ./dist/install.yaml

.PHONY: dist
dist: manifests kustomize ko ## Generate a consolidated YAML with CRDs and deployment.
	mkdir -p dist
	$(KUSTOMIZE) build config/default | $(KO) resolve ${LOCAL} --insecure-registry --base-import-paths=true --platform=$(PLATFORM) --tags=$(TAG) --image-label commit=$(COMMIT),branch=$(BRANCH) $(KO_OPTS) -f - > dist/install.yaml
ifeq (, $(shell which dpkg))
	sed -i '' 's/build-harbor.alauda.cn/registry.alauda.cn:60070/g' dist/install.yaml
else
	sed -i'' 's/build-harbor.alauda.cn/registry.alauda.cn:60070/g' dist/install.yaml
endif

.PHONY: ko-build
ko-build: ko ## use ko to build different compoent image. you can use it like `C=controller make ko-build` or `C=docs make ko-build`
	ko build github.com/AlaudaDevops/experimental/harbor-robot-gen/cmd/$(C) --insecure-registry --bare --platform=$(PLATFORM) --tags=$(TAG)  --image-label commit=$(COMMIT),branch=$(BRANCH) $(KO_OPTS)

.PHONY: htmlreport
GO_TEST_HTML_REPORT ?= $(TOOLBIN)/go-test-report
htmlreport: $(GO_TEST_HTML_REPORT) ##@Development open test report
	cat test.json | $(GO_TEST_HTML_REPORT)
GO_TEST_HTML_REPORT_VERSION ?= v0.9.3
$(GO_TEST_HTML_REPORT): $(LOCALBIN)
	$(call go-install-tool,$(GO_TEST_HTML_REPORT),github.com/vakenbolt/go-test-report,$(GO_TEST_HTML_REPORT_VERSION))

test-with-report: test htmlreport ##@Development Run source code tests and generate html report

docs-npm-install: ##@Docs Install docs requirements
	npm install

docs-npm-dev: ## @Docs Run docs dev server
	npm run dev

docs-npm-build: ## @Docs Build docs server
	npm run build

docs-build: docs-npm-build docs-godoc-build ##@Docs Build all docs site

docs-npm-serve: ## @Docs Serve docs site
	npm run serve

docs-serve: docs-build ##@Docs Run document server
	go run cmd/docs/main.go --port 8181

DOC2GO ?= $(TOOLBIN)/doc2go
DOC2GO_VESION ?= v0.8.1
docs-godoc-build:
	$(call go-install-tool,$(DOC2GO),go.abhg.dev/doc2go,$(DOC2GO_VESION))
	$(DOC2GO)  -out ./dist/godocs ./...

.PHONY: update-chart-images
update-chart-images: yq
	YQ=$(YQ) ./hack/update_chart_image_tags.sh

.PHONY: chart
chart: yq update-chart-images
	cp dist/install.yaml ./chart/templates/
	./hack/extract_crds.sh ./dist/install.yaml ./chart/crds

	# Delete all CustomResourceDefinition resources from install.yaml
	$(YQ) -i 'select(.kind != "CustomResourceDefinition")' ./chart/templates/install.yaml

	# Modify Deployment resources
	$(YQ) -i 'select(.kind == "Deployment").spec.template.spec.containers[0].image = "{{ template \"plugin.image\" . }}"' ./chart/templates/install.yaml
	$(YQ) -i 'select(.kind == "Deployment").spec.template.spec.containers[0].imagePullPolicy = "{{ template \"plugin.imagePolicy\" . }}"' ./chart/templates/install.yaml
	$(YQ) -i 'select(.kind == "Deployment").spec.template.metadata.labels.version-hash = "sha1-{{ .Chart.Version | sha1sum }}"' ./chart/templates/install.yaml

	# Modify Namespace resources
	$(YQ) -i 'select(.kind == "Namespace").metadata.annotations["helm.sh/resource-policy"] = "keep"' ./chart/templates/install.yaml

	# Modify moduleplugin.yaml
	$(YQ) -i 'select(.kind == "ModulePlugin").spec.appReleases[0].chartVersions[0].version = "$(CHART_VERSION)"' ./chart/module-plugin.yaml

	git diff ./chart

helm-lint:
	helm lint ./chart
	helm template debug ./chart > /dev/null 2>&1

package-chart:
	TEMP_DIR=$$(mktemp -d) && \
	cd $$TEMP_DIR && \
	violet create harbor-robot-gen --artifact registry.alauda.cn:60070/devops/chart-harbor-robot-gen:$(CHART_VERSION) --no-auth --platforms linux/amd64 --platforms linux/arm64 && \
	violet package harbor-robot-gen --no-auth
