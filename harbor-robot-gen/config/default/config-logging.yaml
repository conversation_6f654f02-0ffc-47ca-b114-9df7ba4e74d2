apiVersion: v1
data:
  loglevel.harbor-robot-gen: info
  loglevel.harbor-robot-gen.harborrobotbinding-controller: info
  loglevel.harbor-robot-gen.harborproject-controller: info
  loglevel.harbor-robot-gen.address-alias-controller: info
  zap-logger-config: |
    {
      "level": "info",
      "development": false,
      "outputPaths": ["stdout"],
      "errorOutputPaths": ["stderr"],
      "encoding": "json",
      "encoderConfig": {
        "timeKey": "ts",
        "levelKey": "level",
        "nameKey": "logger",
        "callerKey": "caller",
        "messageKey": "msg",
        "stacktraceKey": "stacktrace",
        "lineEnding": "",
        "levelEncoder": "",
        "timeEncoder": "iso8601",
        "durationEncoder": "",
        "callerEncoder": ""
      }
    }
kind: ConfigMap
metadata:
  name: config-logging
