---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: mutating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /mutate-harbor-robot-gen-alaudadevops-alauda-io-v1alpha1-harborrobotbinding
  failurePolicy: Fail
  name: mharborrobotbinding.kb.io
  rules:
  - apiGroups:
    - harbor-robot-gen.alaudadevops.alauda.io
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - harborrobotbindings
  sideEffects: None
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: validating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-harbor-robot-gen-alaudadevops-alauda-io-v1alpha1-harborrobotbinding
  failurePolicy: Fail
  name: vharborrobotbinding.kb.io
  rules:
  - apiGroups:
    - harbor-robot-gen.alaudadevops.alauda.io
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - harborrobotbindings
  sideEffects: None
