
1. 允许 定义 生成的 k8s secret 的名字
2. Harbor 上同名项目 来自 ns 或者 ns 所属 project (来自label 或其他) 是可配置的。
3. 集群可能有多个 Harbor的情况。 当用户接入一个新的Harbor， 用户先创建一个保存Harbor 配置信息的 Secret
4. 资源名称调整。
5. 按照独立的插件来考虑可以增加Harbor 的关键字，表达是和 harbor 强关联的。

场景：
背景： 一个集群可能有多个 Harbor的情况。
用户使用: 
1. 集群管理员可以创建一个 Secret 资源保存 某个 Harbor的配置信息。（认证， 地址）
2. 集群管理员， 可以通过创建集群级别的 HarborRobotBinding 资源， 来约束，
    某些 NS 能且仅能够以最小且定期轮转的权限拉取（或其他由管理员指定的权限） 来操作指定的 Harbor Project 的 资源（镜像等）


假设集群接入了一个Harbor， 想要让 ns-1 的用户仅能拉取 harbor ns-1 project 下镜像的 操作流程

1. 创建 secret, 保存该harbor 信息. (harbor secret 可推荐是 robot)
2. 创建 集群 HarborRobotBinding 资源
    - 指定 namespace 的范围 （ns-1, ns-2）（status 中可考虑记录 发现的 NS）(成功失败的记录？)
    - 指定访问的是哪个 Harbor ， harbor 的哪个 Project
    - 指定访问的权限
    - 指定是否要关联 SA， 以及关联的 SA的名称。 （可选）
    - 指定生成的 secret的名称 （基于 secret 增加若干事件，可以在 secret 增加 annotations, 记录刷新信息）
    - 指定其他 凭据论转相关的配置信息。
    - 看一下 robot account 有没有数量限制。
    - 为不同的 ns 创建 robot account 有利于审计

3. 自动创建同名 project 使用单独的 controller 来管理
4. 
  4.1 Harbor 相关的配置进行数据结构标准化， 通过 API 标准层完成工具类型的扩展。
    4.1.1 Connectors-X 中实现 API 标准层。
    4.1.2 类似 ResolutionRequest 保存请求结果， 变相进行 api 调度或其他逻辑。需要处理敏感信息
       (表达期望的数据结构是标准的，是否是独立资源时两种形式) 


  4.2 提供标准中间的资源期望描述，通过不同工具的 Controller 来完成资源描述的期望。


不和 ACP 的概念耦合。
---
apiVersion: harbor-robot-gen.alaudadevops.alauda.io/v1alpha1
kind: RobotAccountBinding
metadata:
  name: robot
spec:
  # which k8s namespace will create robot account
  serviceAccount:
    name: xxx
  namespace:
    selector:
      matchLabels:
        cpaas.io/project: project-cjt
    secretName: xx
  harbor:
    project: project-cjt
    robot:
      name: robot-injection
      access:
      - action: pull
        resource: repository
    secret:
      name: harborrobotbinding
      namespace: cpaas-system


  # which harbor project will create robot account
  project: project-cjt
  # which harbor will create robot account
  connector:
    name: harborrobotbinding
    namespace: cpaas-system


---
kind: HarborRobotBinding
apiVersion: harbor-robot-gen.alaudadevops.alauda.io/v1alpha1
metadata:
  name: demo
spec:
  sa: default

  harbor:
    project: project-cjt
    robot:
      name: robot-injection
      access:
      - action: pull
        resource: repository
    connector:
      name: harborrobotbinding
      namespace: cpaas-system
    secret:
      name: harborrobotbinding
      namespace: cpaas-system
  refreshInterval: 10m
  serviceAccount:
    name: default
---
kind: Secret
apiVersion: v1
metadata:
  name: harborrobotbinding
  namespace: cpaas-system
type: kubernetes.io/opaque
stringData:
  username: admin
  password: 07Apples@
  url: https://devops-harbor.alaudatech.net
---
