---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: harborrobotbindings.harbor-robot-gen.alaudadevops.alauda.io
spec:
  group: harbor-robot-gen.alaudadevops.alauda.io
  names:
    categories:
    - alauda
    kind: HarborRobotBinding
    listKind: HarborRobotBindingList
    plural: harborrobotbindings
    singular: harborrobotbinding
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.conditions[?(@.type=='Ready')].status
      name: Ready
      type: string
    - jsonPath: .status.lastRefreshTime
      name: LastRefreshTime
      type: string
    - jsonPath: .status.nextRefreshTime
      name: NextRefreshTime
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: HarborRobotBinding is the Schema for the harborrobotbindings
          API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: HarborRobotBindingSpec defines the desired state of HarborRobotBinding
            properties:
              generatedSecret:
                description: GeneratedSecret specifies the secret to be generated
                properties:
                  name:
                    description: Name is the name of the secret
                    type: string
                type: object
              harbor:
                description: Harbor specifies the harbor configuration
                properties:
                  project:
                    description: Project is the name of the harbor project
                    type: string
                  robot:
                    description: Robot specifies the robot account configuration
                    properties:
                      access:
                        description: Access specifies the access permissions
                        items:
                          description: AccessSpec defines the access permission
                          properties:
                            action:
                              description: Action is the action type
                              type: string
                            resource:
                              description: Resource is the resource type
                              type: string
                          type: object
                        type: array
                      name:
                        description: |-
                          Name is the name of the robot account
                          if empty, will use the name of the harborrobotbinding
                        type: string
                    type: object
                  secret:
                    description: |-
                      Secret is the secret reference of the harbor, it will used to create robot account and rotate robot secret
                      should have `username` and `password` for harbor username and password
                      and `url` for harbor url
                    properties:
                      name:
                        description: name is unique within a namespace to reference
                          a secret resource.
                        type: string
                      namespace:
                        description: namespace defines the space within which the
                          secret name must be unique.
                        type: string
                    type: object
                    x-kubernetes-map-type: atomic
                required:
                - secret
                type: object
              namespaces:
                description: Namespaces specifies the namespace selector
                properties:
                  names:
                    description: Names is the list of namespace names
                    items:
                      type: string
                    type: array
                  selector:
                    description: Selector is the label selector
                    properties:
                      matchExpressions:
                        description: matchExpressions is a list of label selector
                          requirements. The requirements are ANDed.
                        items:
                          description: |-
                            A label selector requirement is a selector that contains values, a key, and an operator that
                            relates the key and values.
                          properties:
                            key:
                              description: key is the label key that the selector
                                applies to.
                              type: string
                            operator:
                              description: |-
                                operator represents a key's relationship to a set of values.
                                Valid operators are In, NotIn, Exists and DoesNotExist.
                              type: string
                            values:
                              description: |-
                                values is an array of string values. If the operator is In or NotIn,
                                the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                the values array must be empty. This array is replaced during a strategic
                                merge patch.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          required:
                          - key
                          - operator
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      matchLabels:
                        additionalProperties:
                          type: string
                        description: |-
                          matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                          map is equivalent to an element of matchExpressions, whose key field is "key", the
                          operator is "In", and the values array contains only "value". The requirements are ANDed.
                        type: object
                    type: object
                    x-kubernetes-map-type: atomic
                type: object
              refreshInterval:
                description: RefreshInterval is the interval to refresh the robot
                  secret
                type: string
              serviceAccount:
                description: ServiceAccount specifies the service account to bind
                  the robot secret
                properties:
                  name:
                    description: Name is the name of the service account
                    type: string
                type: object
            type: object
          status:
            description: HarborRobotBindingStatus defines the observed state of HarborRobotBinding
            properties:
              annotations:
                additionalProperties:
                  type: string
                description: |-
                  Annotations is additional Status fields for the Resource to save some
                  additional State as well as convey more information to the user. This is
                  roughly akin to Annotations on any k8s resource, just the reconciler conveying
                  richer information outwards.
                type: object
              conditions:
                description: Conditions the latest available observations of a resource's
                  current state.
                items:
                  description: |-
                    Condition defines a readiness condition for a Knative resource.
                    See: https://github.com/kubernetes/community/blob/master/contributors/devel/sig-architecture/api-conventions.md#typical-status-properties
                  properties:
                    lastTransitionTime:
                      description: |-
                        LastTransitionTime is the last time the condition transitioned from one status to another.
                        We use VolatileTime in place of metav1.Time to exclude this from creating equality.Semantic
                        differences (all other things held constant).
                      type: string
                    message:
                      description: A human readable message indicating details about
                        the transition.
                      type: string
                    reason:
                      description: The reason for the condition's last transition.
                      type: string
                    severity:
                      description: |-
                        Severity with which to treat failures of this type of condition.
                        When this is not specified, it defaults to Error.
                      type: string
                    status:
                      description: Status of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: Type of condition.
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
              lastRefreshTime:
                description: LastRefreshTime is the last time the robot secret was
                  refreshed
                format: date-time
                type: string
              nextRefreshTime:
                description: NextRefreshTime is the next time the robot secret will
                  be refreshed
                format: date-time
                type: string
              observedGeneration:
                description: |-
                  ObservedGeneration is the 'Generation' of the Service that
                  was last processed by the controller.
                format: int64
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
