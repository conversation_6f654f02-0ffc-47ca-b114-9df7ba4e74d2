---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - namespaces
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  - serviceaccounts
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - harbor-robot-gen.alaudadevops.alauda.io
  resources:
  - harborrobotbindings
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - harbor-robot-gen.alaudadevops.alauda.io
  resources:
  - harborrobotbindings/finalizers
  verbs:
  - update
- apiGroups:
  - harbor-robot-gen.alaudadevops.alauda.io
  resources:
  - harborrobotbindings/status
  verbs:
  - get
  - patch
  - update
