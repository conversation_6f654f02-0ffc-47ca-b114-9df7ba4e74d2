/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	v1alpha1 "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/apis/v1alpha1"
	"github.com/AlaudaDevops/pkg/webhook/admission"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"

	// +kubebuilder:scaffold:imports

	controllers "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/controllers"
	"github.com/AlaudaDevops/pkg/sharedmain"
)

var (
	scheme = runtime.NewScheme()
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))

	utilruntime.Must(v1alpha1.AddToScheme(scheme))
	// +kubebuilder:scaffold:scheme
}

func main() {
	sharedmain.App("harbor-robot-gen").
		Scheme(scheme).
		Log().
		Profiling().
		ConfigManager().
		WithFieldIndexer().
		Controllers(
			&controllers.HarborRobotBindingReconciler{},
			&controllers.HarborProjectReconciler{},
			&controllers.AddressAliasReconciler{},
		).
		Webhooks(
			admission.NewDefaulterWebhook(&v1alpha1.HarborRobotBinding{}),
			admission.NewValidatorWebhook(&v1alpha1.HarborRobotBinding{}),
		).
		Run()
}
