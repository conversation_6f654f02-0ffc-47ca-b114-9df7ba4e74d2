/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

// HarborRobotBinding Controller
// +kubebuilder:rbac:groups=harbor-robot-gen.alaudadevops.alauda.io,resources=harborrobotbindings,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=harbor-robot-gen.alaudadevops.alauda.io,resources=harborrobotbindings/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=harbor-robot-gen.alaudadevops.alauda.io,resources=harborrobotbindings/finalizers,verbs=update
// +kubebuilder:rbac:groups=``,resources=serviceaccounts,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=``,resources=secrets,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=``,resources=events,verbs=create;update;patch;get;list;watch

// HarborProject Controller
// +kubebuilder:rbac:groups=``,resources=namespaces,verbs=get;list;watch
// +kubebuilder:rbac:groups=harbor-robot-gen.alaudadevops.alauda.io,resources=harborrobotbindings,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=``,resources=secrets,verbs=get;list;watch;

// AddressAlias Controller
// +kubebuilder:rbac:groups=``,resources=namespaces,verbs=get;list;watch
// +kubebuilder:rbac:groups=harbor-robot-gen.alaudadevops.alauda.io,resources=harborrobotbindings,verbs=get;list;watch;
// +kubebuilder:rbac:groups=``,resources=serviceaccounts,verbs=get;list;watch;update;patch;
// +kubebuilder:rbac:groups=``,resources=secrets,verbs=get;list;watch;create;update;patch;delete
