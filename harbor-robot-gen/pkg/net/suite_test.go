/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package net_test

import (
	"context"
	"testing"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"knative.dev/pkg/logging"

	gozap "go.uber.org/zap"
	// +kubebuilder:scaffold:imports
)

// These tests use Ginkgo (BDD-style Go testing framework). Refer to
// http://onsi.github.io/ginkgo/ to learn more about Ginkgo.

var log *gozap.SugaredLogger
var ctx context.Context

func TestControllers(t *testing.T) {
	RegisterFailHandler(Fail)

	RunSpecs(t, "Harbor Robot Account PKG Net Suite")
}

var _ = BeforeSuite(func() {
	logger, err := gozap.NewDevelopment()
	Expect(err).NotTo(HaveOccurred())
	log = logger.Sugar()

	ctx = context.Background()
	ctx = logging.WithLogger(ctx, log)

	Expect(err).NotTo(HaveOccurred())
})

var _ = AfterSuite(func() {

})
