/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Package net provides network-related utilities.
package net

import (
	"context"
	"fmt"
	"net"
	"net/url"
	"slices"
	"sort"
	"strings"
	"time"

	"knative.dev/pkg/logging"
)

// parseHost extracts the hostname from a registry address (which might be a URL or just a hostname)
func parseHost(address string) (string, error) {
	// If the address doesn't have a scheme, try to parse it as is
	if !strings.Contains(address, "://") {
		// Check if it's just a hostname or hostname:port
		host, _, err := net.SplitHostPort(address)
		if err != nil {
			// If SplitHostPort fails, assume it's just a hostname
			return address, nil
		}
		return host, nil
	}

	// Parse as URL
	parsedURL, err := url.Parse(address)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL: %w", err)
	}

	host, _, err := net.SplitHostPort(parsedURL.Host)
	if err != nil {
		// If SplitHostPort fails, assume there's no port
		return parsedURL.Host, nil
	}
	return host, nil
}

// ResolveIPs resolves a hostname to IP addresses
func ResolveIPs(ctx context.Context, host string, withCache bool) ([]net.IP, error) {
	// Check if it's already an IP address
	if ip := net.ParseIP(host); ip != nil {
		return []net.IP{ip}, nil
	}

	// Resolve hostname to IPs
	if withCache {
		ips, err := net.LookupIP(host)
		if err != nil {
			return nil, fmt.Errorf("failed to lookup IP for host %s: %w", host, err)
		}

		return ips, nil
	}

	resolver := &net.Resolver{
		PreferGo: true,
		Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
			d := net.Dialer{
				Timeout: time.Millisecond * 200,
			}
			return d.DialContext(ctx, network, address)
		},
	}

	ipAddrs, err := resolver.LookupIPAddr(ctx, host)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve IPs for host %s: %w", host, err)
	}

	ips := make([]net.IP, len(ipAddrs))
	for i, ipAddr := range ipAddrs {
		ips[i] = ipAddr.IP
	}

	return ips, nil
}

// ResolveIPsWithUrl extracts the hostname from a URL/address and resolves it to IP addresses
func ResolveIPsWithUrl(ctx context.Context, address string, withCache bool) ([]net.IP, error) {
	host, err := parseHost(address)
	if err != nil {
		return nil, fmt.Errorf("failed to extract host from address %s: %w", address, err)
	}

	ips, err := ResolveIPs(ctx, host, withCache)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve IPs for address %s: %w", address, err)
	}

	return ips, nil
}

// IsIPResolvedEqual checks if the IP addresses of the two addresses are equal
func IsIPResolvedEqual(ctx context.Context, address, alias string, withCache bool) (bool, error) {
	log := logging.FromContext(ctx)

	// Resolve IPs for both addresses
	registryIPs, err := ResolveIPsWithUrl(ctx, address, withCache)
	if err != nil {
		log.Debugw("failed to resolve IPs for registry address", "registryAddress", address, "error", err)
		return false, err
	}

	aliasIPs, err := ResolveIPsWithUrl(ctx, alias, withCache)
	if err != nil {
		log.Debugw("failed to resolve IPs for address alias", "addressAlias", alias, "error", err)
		return false, err
	}

	// Convert IPs to sorted string slices for comparison
	registryIPStrs := make([]string, len(registryIPs))
	aliasIPStrs := make([]string, len(aliasIPs))

	for i, ip := range registryIPs {
		registryIPStrs[i] = ip.String()
	}
	for i, ip := range aliasIPs {
		aliasIPStrs[i] = ip.String()
	}

	// Sort both slices
	sort.Strings(registryIPStrs)
	sort.Strings(aliasIPStrs)

	// Compare using slices.Equal
	isEqual := slices.Equal(registryIPStrs, aliasIPStrs)

	if isEqual {
		log.Debugw("IP is equal", "address", address, "alias", alias, "ip", strings.Join(registryIPStrs, ","))
	} else {
		log.Infow("IP is not equal", "address", address, "ip", strings.Join(registryIPStrs, ","), "alias", alias, "aliasIP", strings.Join(aliasIPStrs, ","))
	}

	return isEqual, nil
}
