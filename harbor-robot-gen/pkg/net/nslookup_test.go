/*
Copyright 2025 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package net_test

import (
	"github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/net"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("IsIPResolvedEqual", func() {

	Context("when the IP addresses are the same", func() {
		It("should return true", func() {
			equal, err := net.IsIPResolvedEqual(ctx, "https://github.com", "https://github.com", false)
			Expect(err).To(BeNil())
			Expect(equal).To(BeTrue())
		})
	})

	Context("when the IP addresses are the same", func() {
		It("should return true", func() {
			equal, err := net.IsIPResolvedEqual(ctx, "https://github.com", "https://api.github.com", false)
			Expect(err).To(BeNil())
			Expect(equal).To(BeFalse())
		})
	})

	Context("when the IP addresses are the same  and address is host", func() {
		It("should return true", func() {
			equal, err := net.IsIPResolvedEqual(ctx, "github.com", "api.github.com", false)
			Expect(err).To(BeNil())
			Expect(equal).To(BeFalse())
		})
	})
})
