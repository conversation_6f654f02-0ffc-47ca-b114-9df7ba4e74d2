/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Package thirdpartyclient provides a client for the third-party API.
package thirdpartyclient

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/go-openapi/swag"
	goharbor "github.com/goharbor/go-client/pkg/harbor"
	goharborProject "github.com/goharbor/go-client/pkg/sdk/v2.0/client/project"
	goharborRobot "github.com/goharbor/go-client/pkg/sdk/v2.0/client/robot"
	goharbormodels "github.com/goharbor/go-client/pkg/sdk/v2.0/models"
	"knative.dev/pkg/logging"
)

type harborClient struct {
	*goharbor.ClientSet
	url      string
	username string
	password string
}

var _ HarborClient = (*harborClient)(nil)

func NewHarborClient(url string, username string, password string) HarborClient {
	url = strings.TrimSuffix(url, "/api/v2.0")
	url = strings.TrimSuffix(url, "/api/v2.0/")
	apiPath := "/api/v2.0"

	csc := goharbor.ClientSetConfig{
		URL:      fmt.Sprintf("%s/%s", strings.TrimSuffix(url, "/"), strings.TrimPrefix(apiPath, "/")),
		Username: username,
		Password: password,
		Insecure: true,
	}

	cs, err := goharbor.NewClientSet(&csc)
	if err != nil {
		return nil
	}

	return &harborClient{
		ClientSet: cs,
		url:       url,
		username:  username,
		password:  password,
	}
}

func (r *harborClient) ListProjects(ctx context.Context) ([]*goharbormodels.Project, error) {
	log := logging.FromContext(ctx)
	log.Debugw("listing harbor projects")

	params := goharborProject.NewListProjectsParams().WithContext(ctx)
	size := int64(100)
	params.SetPageSize(&size)
	params.SetWithDetail(swag.Bool(true))
	response, err := r.V2().Project.ListProjects(ctx, params)
	if err != nil {
		log.Errorw("failed to list projects", "error", err)
		return nil, err
	}

	projects := response.GetPayload()
	log.Infow("listed harbor projects", "count", len(projects))
	return projects, nil
}

func (r *harborClient) GetRobotAccounts(ctx context.Context, projectID int) ([]*HarborRobotBinding, error) {
	log := logging.FromContext(ctx)
	log.Debugw("getting robot accounts", "project-id", projectID)

	q := fmt.Sprintf("Level=project,ProjectID=%d", projectID)
	list, err := r.V2().Robot.ListRobot(ctx, &goharborRobot.ListRobotParams{
		Q: &q,
	})
	if err != nil {
		log.Errorw("failed to get robot accounts", "error", err)
		return nil, err
	}

	log.Infow("got robot accounts", "project-id", projectID, "count", len(list.Payload))
	robots := make([]*HarborRobotBinding, len(list.Payload))
	for i, robot := range list.Payload {
		robots[i] = &HarborRobotBinding{
			CreationTime: robot.CreationTime,
			ExpiresAt:    robot.ExpiresAt,
			ID:           robot.ID,
			Name:         robot.Name,
			Secret:       robot.Secret,
		}
	}

	return robots, nil
}

func (r *harborClient) CreateRobotAccount(ctx context.Context, robotCreate goharbormodels.RobotCreate) (*HarborRobotBinding, error) {
	log := logging.FromContext(ctx)
	log.Debugw("creating robot account", "name", robotCreate.Name, "robotCreate", robotCreate)

	params := goharborRobot.NewCreateRobotParams().WithContext(ctx)
	params.SetRobot(&robotCreate)

	resp, err := r.V2().Robot.CreateRobot(ctx, params)
	if err != nil {
		log.Errorw("failed to create robot account", "error", err)
		return nil, err
	}

	log.Infow("created robot account", "name", resp.Payload.Name, "id", resp.Payload.ID)
	return &HarborRobotBinding{
		CreationTime: resp.Payload.CreationTime,
		ExpiresAt:    resp.Payload.ExpiresAt,
		ID:           resp.Payload.ID,
		Name:         resp.Payload.Name,
		Secret:       resp.Payload.Secret,
	}, nil
}

func (r *harborClient) RefreshRobotAccount(ctx context.Context, projectID int, robotID int, params goharborRobot.RefreshSecParams) (token string, err error) {
	log := logging.FromContext(ctx).With("project-id", projectID, "robot-id", robotID)
	log.Debugw("refreshing robot account")
	if params.RobotSec == nil {
		params.RobotSec = &goharbormodels.RobotSec{}
	}

	resp, err := r.V2().Robot.RefreshSec(ctx, &params)
	if err != nil {
		log.Errorw("failed to refresh robot account", "error", err)
		return "", err
	}

	log.Infow("refreshed robot account", "project-id", projectID, "robot-id", robotID)
	return resp.Payload.Secret, nil
}

func (r *harborClient) DeleteRobotAccount(ctx context.Context, projectID int, robotID int) error {
	log := logging.FromContext(ctx).With("project-id", projectID, "robot-id", robotID)
	log.Debugw("deleting robot account")

	_, err := r.V2().Robot.DeleteRobot(ctx, &goharborRobot.DeleteRobotParams{
		RobotID: int64(robotID),
	})
	if err != nil {
		if errors.Is(err, &goharborRobot.DeleteRobotNotFound{}) {
			return nil
		}
		log.Errorw("failed to delete robot account", "error", err)
		return err
	}

	log.Infow("deleted robot account", "project-id", projectID, "robot-id", robotID)
	return nil
}

func (r *harborClient) GetBaseUrl() string {
	return r.url
}

func (r *harborClient) CreateProject(ctx context.Context, projectCreate goharborProject.CreateProjectParams) (*goharbormodels.Project, error) {
	log := logging.FromContext(ctx)
	log.Debugw("creating project", "project-create", projectCreate)

	_, err := r.V2().Project.CreateProject(ctx, &projectCreate)
	if err != nil {
		log.Errorw("failed to create project", "error", err)
		return nil, err
	}

	log.Infow("created project", "project-name", projectCreate.Project.ProjectName)

	projects, err := r.ListProjects(ctx)
	if err != nil {
		log.Errorw("failed to list projects", "error", err)
		return nil, err
	}
	for _, project := range projects {
		if project.Name == projectCreate.Project.ProjectName {
			return project, nil
		}
	}

	return nil, nil
}
