/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package thirdpartyclient

import (
	"context"

	"github.com/go-openapi/strfmt"
	_ "github.com/goharbor/go-client/pkg/harbor"
	goharborProject "github.com/goharbor/go-client/pkg/sdk/v2.0/client/project"
	goharborRobot "github.com/goharbor/go-client/pkg/sdk/v2.0/client/robot"
	goharbormodels "github.com/goharbor/go-client/pkg/sdk/v2.0/models"
	// goharborproject "github.com/goharbor/go-client/pkg/sdk/v2.0/models/project"
)

type HarborClient interface {
	CreateProject(ctx context.Context, projectCreate goharborProject.CreateProjectParams) (*goharbormodels.Project, error)
	ListProjects(ctx context.Context) ([]*goharbormodels.Project, error)
	GetRobotAccounts(ctx context.Context, projectID int) ([]*HarborRobotBinding, error)
	CreateRobotAccount(ctx context.Context, robotCreate goharbormodels.RobotCreate) (*HarborRobotBinding, error)
	RefreshRobotAccount(ctx context.Context, projectID int, robotID int, params goharborRobot.RefreshSecParams) (string, error)
	DeleteRobotAccount(ctx context.Context, projectID int, robotID int) error

	GetBaseUrl() string
}

type HarborRobotBinding struct {

	// The creation time of the robot.
	// Format: date-time
	CreationTime strfmt.DateTime `json:"creation_time,omitempty"`

	// The expiration date of the robot
	ExpiresAt int64 `json:"expires_at,omitempty"`

	// The ID of the robot
	ID int64 `json:"id,omitempty"`

	// The name of the robot
	Name string `json:"name,omitempty"`

	// The secret of the robot
	// when get robot account, this field is empty
	// it will be filled when create robot account
	Secret string `json:"secret,omitempty"`
}
