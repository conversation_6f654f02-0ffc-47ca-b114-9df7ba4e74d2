package thirdpartyclient

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	goharborRobot "github.com/goharbor/go-client/pkg/sdk/v2.0/client/robot"
	goharbormodels "github.com/goharbor/go-client/pkg/sdk/v2.0/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupTestClient creates a test Harbor client
func setupTestClient(t *testing.T) HarborClient {
	// Get Harbor configuration from environment variables
	url := os.Getenv("HARBOR_URL")
	username := os.Getenv("HARBOR_USERNAME")
	password := os.Getenv("HARBOR_PASSWORD")

	if url == "" || username == "" || password == "" {
		t.Skip("Skipping test. Set HARBOR_URL, HARBOR_USERNAME, and HARBOR_PASSWORD to run")
	}

	// Create client
	client := NewHarborClient(url, username, password)
	if client == nil {
		t.Fatal("Failed to create Harbor client")
	}

	return client
}

// getTestProject retrieves a test project
func getTestProject(t *testing.T, client HarborClient) *goharbormodels.Project {
	projects, err := client.ListProjects(context.Background())
	if err != nil {
		t.Fatalf("Failed to list projects: %v", err)
	}
	if len(projects) == 0 {
		t.Fatal("Need at least one project to run tests")
	}
	for _, project := range projects {
		if project.Name == os.Getenv("HARBOR_PROJECT") {
			return project
		}
	}
	return nil
}

// TestListProjects tests the project listing functionality
func TestListProjects(t *testing.T) {
	client := setupTestClient(t)

	// Test listing projects
	projects, err := client.ListProjects(context.Background())
	fmt.Println(projects)
	if err != nil {
		t.Fatalf("Failed to list projects: %v", err)
	}
	if len(projects) == 0 {
		t.Fatal("Should have at least one project")
	}

	// Validate returned data
	for _, p := range projects {
		t.Logf("Found project: %s (ID: %d)", p.Name, p.ProjectID)
		if p.Name == "" {
			t.Error("Project name should not be empty")
		}
		if p.ProjectID == 0 {
			t.Error("Project ID should not be 0")
		}
	}
}

// TestGetRobotAccounts tests the robot account listing functionality
func TestGetRobotAccounts(t *testing.T) {
	client := setupTestClient(t)
	project := getTestProject(t, client)

	// Get robot account list
	robots, err := client.GetRobotAccounts(context.Background(), int(project.ProjectID))
	if err != nil {
		t.Fatalf("Failed to get robot accounts: %v", err)
	}

	// Validate returned data
	for _, robot := range robots {
		t.Logf("Found robot: %s (ID: %d)", robot.Name, robot.ID)
		if robot.Name == "" {
			t.Error("Robot account name should not be empty")
		}
		if robot.ID == 0 {
			t.Error("Robot account ID should not be 0")
		}
		if robot.CreationTime.String() == "" {
			t.Error("Creation time should not be empty")
		}
	}
}

func fmtRobotName(projectName, name string) string {
	return fmt.Sprintf("robot$%s+%s", projectName, name)
}

// TestCreateAndDeleteRobotAccount tests the creation and deletion of robot accounts
func TestCreateAndDeleteRobotAccount(t *testing.T) {
	client := setupTestClient(t)
	project := getTestProject(t, client)

	// Create robot account
	robotName := fmt.Sprintf("test-robot-%d", time.Now().UnixNano())

	robotCreate := goharbormodels.RobotCreate{
		Name:        robotName,
		Level:       "project",
		Description: "Test robot account",
		Duration:    -1,
		Permissions: []*goharbormodels.RobotPermission{
			{
				Access: []*goharbormodels.Access{
					{
						Resource: "repository",
						Action:   "pull",
					},
				},
				Kind:      "project",
				Namespace: project.Name,
			},
		},
	}

	robot, err := client.CreateRobotAccount(context.Background(), robotCreate)
	require.NoError(t, err)
	require.NotNil(t, robot)
	assert.NotEmpty(t, robot.Name, "机器人账号应该有名称")
	assert.NotZero(t, robot.ID, "机器人账号应该有 ID")

	// Test refreshing robot account
	params := goharborRobot.RefreshSecParams{
		RobotID: robot.ID,
	}
	token, err := client.RefreshRobotAccount(context.Background(), int(project.ProjectID), int(robot.ID), params)
	if err != nil {
		t.Fatalf("Failed to refresh robot account: %v", err)
	}
	if token == "" {
		t.Fatal("Refreshed token should not be empty")
	}
	t.Logf("Created robot account: %s", robot.Name)

	// Delete robot account
	err = client.DeleteRobotAccount(context.Background(), int(project.ProjectID), int(robot.ID))
	if err != nil {
		t.Fatalf("Failed to delete robot account: %v", err)
	}

	// Verify account has been deleted
	robots, err := client.GetRobotAccounts(context.Background(), int(project.ProjectID))
	if err != nil {
		t.Fatalf("Failed to get robot accounts: %v", err)
	}
	for _, r := range robots {
		if r.ID == robot.ID {
			t.Errorf("Robot account %d should have been deleted", robot.ID)
		}
	}
}
