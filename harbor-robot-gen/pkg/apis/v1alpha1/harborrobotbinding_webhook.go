/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"context"

	"github.com/AlaudaDevops/pkg/webhook/admission"

	"github.com/AlaudaDevops/pkg/apis/validation"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/validation/field"
	"knative.dev/pkg/logging"
)

//+kubebuilder:webhook:path=/mutate-harbor-robot-gen-alaudadevops-alauda-io-v1alpha1-harborrobotbinding,mutating=true,failurePolicy=fail,sideEffects=None,groups=harbor-robot-gen.alaudadevops.alauda.io,resources=harborrobotbindings,verbs=create;update,versions=v1alpha1,name=mharborrobotbinding.kb.io,admissionReviewVersions=v1

var _ admission.Defaulter = &HarborRobotBinding{}

// Default implements webhook.Defaulter so a webhook will be registered for the type
func (c *HarborRobotBinding) Default(ctx context.Context) {
	log := logging.FromContext(ctx).With("harborrobotbinding", c.Name)

	log.Info("default")

	if c.DeletionTimestamp != nil {
		log.Infow("the harborrobotbinding is about to be deleted, so the process of Default is skipped", "name", c.Name)
		return
	}
}

//+kubebuilder:webhook:path=/validate-harbor-robot-gen-alaudadevops-alauda-io-v1alpha1-harborrobotbinding,mutating=false,failurePolicy=fail,sideEffects=None,groups=harbor-robot-gen.alaudadevops.alauda.io,resources=harborrobotbindings,verbs=create;update,versions=v1alpha1,name=vharborrobotbinding.kb.io,admissionReviewVersions=v1

var _ admission.Validator = &HarborRobotBinding{}

// ValidateCreate validate HarborRobotBinding creating
func (r *HarborRobotBinding) ValidateCreate(ctx context.Context) error {
	log := logging.FromContext(ctx).With("harborrobotbinding", r.Name)
	log.Info("validate create")
	return validation.ReturnInvalidError(HarborRobotBindingGK, r.GetName(), r.Validate(ctx))
}

// ValidateUpdate validate HarborRobotBinding updating
func (r *HarborRobotBinding) ValidateUpdate(ctx context.Context, old runtime.Object) error {
	log := logging.FromContext(ctx).With("harborrobotbinding", r.Name)
	log.Info("validate update")
	if r.DeletionTimestamp != nil {
		log.Debugw("delete timestamp is not nil, skip validate")
		return nil
	}
	return validation.ReturnInvalidError(HarborRobotBindingGK, r.GetName(), r.Validate(ctx))
}

// ValidateDelete validate HarborRobotBinding deleting
func (r *HarborRobotBinding) ValidateDelete(ctx context.Context) error {
	return nil
}

// Validate validate HarborRobotBinding
func (c *HarborRobotBinding) Validate(ctx context.Context) field.ErrorList {
	errs := field.ErrorList{}
	errs = append(errs, validation.ValidateCommonObject(c)...)
	errs = append(errs, c.Spec.Validate(ctx, c, field.NewPath("spec"))...)
	return errs
}

// Validate validate HarborRobotBindingSpec
func (spec *HarborRobotBindingSpec) Validate(ctx context.Context, harborrobotbinding *HarborRobotBinding, path *field.Path) field.ErrorList {
	errs := field.ErrorList{}

	errs = append(errs, spec.Harbor.Validate(ctx, path.Child("harbor"))...)

	if spec.RefreshInterval == nil {
		errs = append(errs, field.Required(path.Child("refreshInterval"), "you must provide refreshInterval"))
	} else {
		if spec.RefreshInterval.Duration.Seconds() <= 0 {
			errs = append(errs, field.Invalid(path.Child("refreshInterval"), spec.RefreshInterval.Duration, "should great then zero"))
		}
	}

	return errs
}

// Validate validate HarborSpec
func (harbor *HarborSpec) Validate(ctx context.Context, path *field.Path) field.ErrorList {
	errs := field.ErrorList{}

	if harbor.Project == "" {
		errs = append(errs, field.Required(path.Child("project"), "project must not be empty"))
	}

	if harbor.Secret == nil || harbor.Secret.Name == "" || harbor.Secret.Namespace == "" {
		errs = append(errs, field.Required(path.Child("secret"), "secret is required, you must provide secret name and secret namesapce"))
	}

	if len(harbor.Robot.Access) == 0 {
		errs = append(errs, field.Required(path.Child("robot", "access"), "access must not be empty"))
	}

	return errs
}
