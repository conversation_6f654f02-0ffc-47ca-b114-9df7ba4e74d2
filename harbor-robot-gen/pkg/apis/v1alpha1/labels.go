/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

const (
	// HarborRobotGenSecretLabel is the label of the harbor robot gen secret
	// it is used to identify the secret is used by harbor-robot-gen to save the harbor connection config
	HarborRobotGenSecretLabel = "harbor-robot-gen" //nolint:gosec

	// HarborRobotGenSecretLabelValue is the value of the harbor robot gen secret label
	HarborRobotGenSecretLabelValue = "true"
)
