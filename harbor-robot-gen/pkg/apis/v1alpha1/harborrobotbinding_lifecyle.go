/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	kmetav1alpha1 "github.com/AlaudaDevops/pkg/apis/meta/v1alpha1"
	"knative.dev/pkg/apis"
)

const (
	HarborRobotAccountCondition apis.ConditionType = "HarborRobotAccount"
	RobotSecretCondition        apis.ConditionType = "RobotSecret"
	SecretSyncConditionSecret   apis.ConditionType = "SecretSync"
)

var harborRobotBindingCondSet = apis.NewLivingConditionSet(
	HarborRobotAccountCondition,
	RobotSecretCondition,
	SecretSyncConditionSecret,
)

// GetConditionSet retrieves the condition set for this resource
func (s *HarborRobotBindingStatus) GetConditionSet() apis.ConditionSet {
	return harborRobotBindingCondSet
}

// InitializeConditions sets relevant unset conditions to Unknown state.
func (s *HarborRobotBindingStatus) InitializeConditions() {
	s.GetConditionSet().Manage(s).InitializeConditions()
}

// GetTopLevelCondition returns the top level Condition.
func (s *HarborRobotBindingStatus) GetTopLevelCondition() *apis.Condition {
	return s.GetConditionSet().Manage(s).GetTopLevelCondition()
}

// GetCondition returns the condition currently associated with the given type, or nil.
func (s *HarborRobotBindingStatus) GetCondition(t apis.ConditionType) *apis.Condition {
	return s.GetConditionSet().Manage(s).GetCondition(t)
}

// IsReady returns true if the resource is ready overall.
func (s *HarborRobotBindingStatus) IsReady() bool {
	return s.GetConditionSet().Manage(s).IsHappy()
}

// SetCondition sets a condition using an error to determine if it is True or False.
// If the given error is not nil will examine its reason and mark the condition as False
// otherwise will set condition to True
func (s *HarborRobotBindingStatus) SetCondition(t apis.ConditionType, err error, reason string) {
	kmetav1alpha1.SetConditionByErrorReason(s.GetConditionSet().Manage(s), t, err, reason)
}

// SetConditionWithMessage sets a condition using an error to determine if it is True or False.
// If the given error is not nil will examine its reason and mark the condition as False
// otherwise will set condition to True
func (s *HarborRobotBindingStatus) SetConditionWithMessage(t apis.ConditionType, err error, reason string, msg string) {
	kmetav1alpha1.SetConditionByErrorReason(s.GetConditionSet().Manage(s), t, err, reason)
	kmetav1alpha1.GetCondition(s.Status.GetConditions(), t).Message = msg
}
