/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"context"

	ktesting "github.com/AlaudaDevops/pkg/testing"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"k8s.io/apimachinery/pkg/util/validation/field"
)

var _ = Describe("HarborRobotBinding Validate", func() {
	var (
		obj *HarborRobotBinding
		err field.ErrorList
		ctx context.Context
	)

	BeforeEach(func() {
		ctx = context.Background()
	})

	JustBeforeEach(func() {
		err = obj.Validate(ctx)
	})

	Context("valid data", func() {
		BeforeEach(func() {
			obj = &HarborRobotBinding{}
			Expect(ktesting.LoadYAML("testdata/harborrobotbinding.success.yaml", obj)).To(Succeed())
		})

		It("validate succeed", func() {
			Expect(len(err)).To(BeZero())
		})
	})

	Context("invalid data", func() {
		BeforeEach(func() {
			obj = &HarborRobotBinding{}
			Expect(ktesting.LoadYAML("testdata/harborrobotbinding.validation.error.yaml", obj)).To(Succeed())
		})

		It("return error", func() {
			fieldsDetails := map[string]string{}
			for _, f := range err {
				fieldsDetails[f.Field] = f.Detail
			}
			Expect(fieldsDetails).Should(BeEquivalentTo(map[string]string{
				"spec.harbor.project":      "project must not be empty",
				"spec.harbor.secret":       "secret is required, you must provide secret name and secret namesapce",
				"spec.harbor.robot.access": "access must not be empty",
				"spec.refreshInterval":     "you must provide refreshInterval",
			}))
		})
	})
})
