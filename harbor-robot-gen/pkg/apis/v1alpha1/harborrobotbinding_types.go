/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	duckv1 "knative.dev/pkg/apis/duck/v1"
)

// HarborRobotBindingSpec defines the desired state of HarborRobotBinding
type HarborRobotBindingSpec struct {
	// RefreshInterval is the interval to refresh the robot secret
	RefreshInterval *metav1.Duration `json:"refreshInterval,omitempty"`

	// ServiceAccount specifies the service account to bind the robot secret
	// +optional
	ServiceAccount *ServiceAccountBindingSpec `json:"serviceAccount,omitempty"`

	// GeneratedSecret specifies the secret to be generated
	// +optional
	GeneratedSecret *GeneratedSecretSpec `json:"generatedSecret,omitempty"`

	// Namespaces specifies the namespace selector
	Namespaces NamespaceSelectorSpec `json:"namespaces,omitempty"`

	// Harbor specifies the harbor configuration
	// +optional
	Harbor HarborSpec `json:"harbor,omitempty"`
}

// NamespaceSelectorSpec defines the namespace selector
type NamespaceSelectorSpec struct {
	// Names is the list of namespace names
	// +optional
	Names []string `json:"names,omitempty"`
	// Selector is the label selector
	// +optional
	Selector *metav1.LabelSelector `json:"selector,omitempty"`
}

// GeneratedSecretSpec defines the generated secret configuration
type GeneratedSecretSpec struct {
	// Name is the name of the secret
	// +optional
	Name string `json:"name,omitempty"`
}

// ServiceAccountBindingSpec defines the service account configuration
type ServiceAccountBindingSpec struct {
	// Name is the name of the service account
	// +optional
	Name string `json:"name,omitempty"`
}

// HarborSpec defines the harbor configuration
type HarborSpec struct {
	// Secret is the secret reference of the harbor, it will used to create robot account and rotate robot secret
	// should have `username` and `password` for harbor username and password
	// and `url` for harbor url
	// +kubebuilder:validation:Required
	Secret *corev1.SecretReference `json:"secret,omitempty"`
	// Project is the name of the harbor project
	// +optional
	Project string `json:"project,omitempty"`

	// Robot specifies the robot account configuration
	// +optional
	Robot RobotSpec `json:"robot,omitempty"`
}

// RobotSpec defines the robot account configuration
type RobotSpec struct {
	// Name is the name of the robot account
	// if empty, will use the name of the harborrobotbinding
	// +optional
	Name string `json:"name,omitempty"`

	// Access specifies the access permissions
	Access []AccessSpec `json:"access,omitempty"`
}

// AccessSpec defines the access permission
type AccessSpec struct {
	// Action is the action type
	// +optional
	Action string `json:"action,omitempty"`

	// Resource is the resource type
	// +optional
	Resource string `json:"resource,omitempty"`
}

// HarborRobotBindingStatus defines the observed state of HarborRobotBinding
type HarborRobotBindingStatus struct {
	// Inherits duck/v1 Status
	duckv1.Status `json:",inline"`

	// LastRefreshTime is the last time the robot secret was refreshed
	// +optional
	LastRefreshTime *metav1.Time `json:"lastRefreshTime,omitempty"`

	// NextRefreshTime is the next time the robot secret will be refreshed
	// +optional
	NextRefreshTime *metav1.Time `json:"nextRefreshTime,omitempty"`
}

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:resource:scope=Cluster,categories={alauda}
// +kubebuilder:printcolumn:name=Ready,type=string,JSONPath=".status.conditions[?(@.type=='Ready')].status"
// +kubebuilder:printcolumn:name=LastRefreshTime,type=string,JSONPath=".status.lastRefreshTime"
// +kubebuilder:printcolumn:name=NextRefreshTime,type=string,JSONPath=".status.nextRefreshTime"
// +kubebuilder:printcolumn:name="Age",JSONPath=`.metadata.creationTimestamp`,type=date

// HarborRobotBinding is the Schema for the harborrobotbindings API
type HarborRobotBinding struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   HarborRobotBindingSpec   `json:"spec,omitempty"`
	Status HarborRobotBindingStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// HarborRobotBindingList contains a list of HarborRobotBinding
type HarborRobotBindingList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []HarborRobotBinding `json:"items"`
}

func init() {
	SchemeBuilder.Register(&HarborRobotBinding{}, &HarborRobotBindingList{})
}
