/*
Copyright 2025 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"time"

	corev1 "k8s.io/api/core/v1"
)

const (
	// HarborConfigSecretKeyUrl is the key of the url in the secret
	HarborConfigSecretKeyUrl = "url"
	// HarborConfigSecretKeyUsername is the key of the username in the secret
	HarborConfigSecretKeyUsername = "username"
	// HarborConfigSecretKeyPassword is the key of the password in the secret
	HarborConfigSecretKeyPassword = "password"
	// HarborConfigSecretKeyAddress<PERSON>lia<PERSON> is the key of the address alias in the secret
	// if you set alias address, it will generate an alias secret and the alias secret will be used to pull image from the alias address
	HarborConfigSecretKeyAddressAlias = "url.alias"
	// HarborConfigSecretKeyAliasPolicy is the key of the alias policy in the secret
	// the optional values are "Always" and "IfIPEqual"
	// Always: always generate an alias secret
	// IfIPEqual: generate an alias secret only if the ip of the alias address is equal to the ip of the url
	HarborConfigSecretKeyAliasPolicy = "url.alias.policy" //nolint:gosec
	// HarborConfigSecretKeyAliasCheckInterval is the key of the alias check interval in the secret
	// the value is a duration string, like "2m"
	HarborConfigSecretKeyAliasCheckInterval = "url.alias.check.interval" //nolint:gosec
	// AliasCheckIntervalDefault is the default value of the alias check interval
	AliasCheckIntervalDefault time.Duration = 1 * time.Minute
)

// HarborAddressAliasPolicy is the policy of generating an alias secret
type HarborAddressAliasPolicy string

const (
	// HarborAddressAliasPolicyAlways indicates that always generate an alias secret using the alias address
	HarborAddressAliasPolicyAlways HarborAddressAliasPolicy = "Always"
	// HarborAddressAliasPolicyIfIPEqual indicates that generate an alias secret only if the ip of the alias address is equal to the ip of the url
	HarborAddressAliasPolicyIfIPEqual HarborAddressAliasPolicy = "IfIPEqual"
)

// HarborConnectionConfig is the config of the harbor connection
type HarborConnectionConfig struct {
	// Url is the url of the harbor
	Url string
	// Username is the username of the harbor
	Username string
	// Password is the password of the harbor
	Password string
	// AddressAlias is the alias address of the harbor
	AddressAlias string
	// AliasPolicy is the policy of generating an alias secret, if the alias address is not set, the policy will be IfIPEqual
	AliasPolicy HarborAddressAliasPolicy
	// AliasCheckInterval is the interval of checking the alias address
	AliasCheckInterval *time.Duration
}

// FromSecret converts the secret to the HarborConnectionConfig
func (config *HarborConnectionConfig) FromSecret(secret *corev1.Secret) error {
	config.Url = string(secret.Data[HarborConfigSecretKeyUrl])
	config.Username = string(secret.Data[HarborConfigSecretKeyUsername])
	config.Password = string(secret.Data[HarborConfigSecretKeyPassword])
	config.AddressAlias = string(secret.Data[HarborConfigSecretKeyAddressAlias])

	if config.AddressAlias != "" {

		config.AliasPolicy = HarborAddressAliasPolicyIfIPEqual

		aliasPolicy := string(secret.Data[HarborConfigSecretKeyAliasPolicy])
		if aliasPolicy != "" {
			config.AliasPolicy = HarborAddressAliasPolicy(aliasPolicy)
		}

		defaultDuration := AliasCheckIntervalDefault
		config.AliasCheckInterval = &defaultDuration

		aliasCheckInterval := string(secret.Data[HarborConfigSecretKeyAliasCheckInterval])
		if aliasCheckInterval != "" {
			duration, err := time.ParseDuration(aliasCheckInterval)
			if err != nil {
				return err
			}

			config.AliasCheckInterval = &duration
		}
	}

	return nil
}
