//go:build !ignore_autogenerated

/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	timex "time"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AccessSpec) DeepCopyInto(out *AccessSpec) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AccessSpec.
func (in *AccessSpec) DeepCopy() *AccessSpec {
	if in == nil {
		return nil
	}
	out := new(AccessSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GeneratedSecretSpec) DeepCopyInto(out *GeneratedSecretSpec) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GeneratedSecretSpec.
func (in *GeneratedSecretSpec) DeepCopy() *GeneratedSecretSpec {
	if in == nil {
		return nil
	}
	out := new(GeneratedSecretSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HarborConnectionConfig) DeepCopyInto(out *HarborConnectionConfig) {
	*out = *in
	if in.AliasCheckInterval != nil {
		in, out := &in.AliasCheckInterval, &out.AliasCheckInterval
		*out = new(timex.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HarborConnectionConfig.
func (in *HarborConnectionConfig) DeepCopy() *HarborConnectionConfig {
	if in == nil {
		return nil
	}
	out := new(HarborConnectionConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HarborRobotBinding) DeepCopyInto(out *HarborRobotBinding) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HarborRobotBinding.
func (in *HarborRobotBinding) DeepCopy() *HarborRobotBinding {
	if in == nil {
		return nil
	}
	out := new(HarborRobotBinding)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *HarborRobotBinding) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HarborRobotBindingList) DeepCopyInto(out *HarborRobotBindingList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]HarborRobotBinding, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HarborRobotBindingList.
func (in *HarborRobotBindingList) DeepCopy() *HarborRobotBindingList {
	if in == nil {
		return nil
	}
	out := new(HarborRobotBindingList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *HarborRobotBindingList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HarborRobotBindingSpec) DeepCopyInto(out *HarborRobotBindingSpec) {
	*out = *in
	if in.RefreshInterval != nil {
		in, out := &in.RefreshInterval, &out.RefreshInterval
		*out = new(v1.Duration)
		**out = **in
	}
	if in.ServiceAccount != nil {
		in, out := &in.ServiceAccount, &out.ServiceAccount
		*out = new(ServiceAccountBindingSpec)
		**out = **in
	}
	if in.GeneratedSecret != nil {
		in, out := &in.GeneratedSecret, &out.GeneratedSecret
		*out = new(GeneratedSecretSpec)
		**out = **in
	}
	in.Namespaces.DeepCopyInto(&out.Namespaces)
	in.Harbor.DeepCopyInto(&out.Harbor)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HarborRobotBindingSpec.
func (in *HarborRobotBindingSpec) DeepCopy() *HarborRobotBindingSpec {
	if in == nil {
		return nil
	}
	out := new(HarborRobotBindingSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HarborRobotBindingStatus) DeepCopyInto(out *HarborRobotBindingStatus) {
	*out = *in
	in.Status.DeepCopyInto(&out.Status)
	if in.LastRefreshTime != nil {
		in, out := &in.LastRefreshTime, &out.LastRefreshTime
		*out = (*in).DeepCopy()
	}
	if in.NextRefreshTime != nil {
		in, out := &in.NextRefreshTime, &out.NextRefreshTime
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HarborRobotBindingStatus.
func (in *HarborRobotBindingStatus) DeepCopy() *HarborRobotBindingStatus {
	if in == nil {
		return nil
	}
	out := new(HarborRobotBindingStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HarborSpec) DeepCopyInto(out *HarborSpec) {
	*out = *in
	if in.Secret != nil {
		in, out := &in.Secret, &out.Secret
		*out = new(corev1.SecretReference)
		**out = **in
	}
	in.Robot.DeepCopyInto(&out.Robot)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HarborSpec.
func (in *HarborSpec) DeepCopy() *HarborSpec {
	if in == nil {
		return nil
	}
	out := new(HarborSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NamespaceSelectorSpec) DeepCopyInto(out *NamespaceSelectorSpec) {
	*out = *in
	if in.Names != nil {
		in, out := &in.Names, &out.Names
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Selector != nil {
		in, out := &in.Selector, &out.Selector
		*out = new(v1.LabelSelector)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NamespaceSelectorSpec.
func (in *NamespaceSelectorSpec) DeepCopy() *NamespaceSelectorSpec {
	if in == nil {
		return nil
	}
	out := new(NamespaceSelectorSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RobotSpec) DeepCopyInto(out *RobotSpec) {
	*out = *in
	if in.Access != nil {
		in, out := &in.Access, &out.Access
		*out = make([]AccessSpec, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RobotSpec.
func (in *RobotSpec) DeepCopy() *RobotSpec {
	if in == nil {
		return nil
	}
	out := new(RobotSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ServiceAccountBindingSpec) DeepCopyInto(out *ServiceAccountBindingSpec) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceAccountBindingSpec.
func (in *ServiceAccountBindingSpec) DeepCopy() *ServiceAccountBindingSpec {
	if in == nil {
		return nil
	}
	out := new(ServiceAccountBindingSpec)
	in.DeepCopyInto(out)
	return out
}
