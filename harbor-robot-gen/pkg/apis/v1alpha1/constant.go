/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

const (

	// LabelKeyProject is the annotation key for project in namespace
	LabelKeyProject = "cpaas.io/project"
	// LabelKeyInnerNamespace is the annotation key for inner namespace in namespace
	LabelKeyInnerNamespace = "cpaas.io/inner-namespace"

	// ConnectorProxySvcNamePrefix is prefix for connector proxy service name
	ConnectorProxySvcNamePrefix = "c"
)
