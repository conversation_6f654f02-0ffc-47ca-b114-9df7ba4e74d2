/*
Copyright 2025 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

var _ = Describe("HarborConnectionConfig.FromSecret", func() {
	var (
		config *HarborConnectionConfig
		secret *corev1.Secret
	)

	BeforeEach(func() {
		config = &HarborConnectionConfig{}
		secret = &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-secret",
				Namespace: "test-namespace",
			},
			Data: map[string][]byte{},
		}
	})

	Context("basic field parsing", func() {
		BeforeEach(func() {
			secret.Data = map[string][]byte{
				HarborConfigSecretKeyUrl:      []byte("https://harbor.example.com"),
				HarborConfigSecretKeyUsername: []byte("admin"),
				HarborConfigSecretKeyPassword: []byte("password123"),
			}
		})

		It("should correctly parse basic fields", func() {
			err := config.FromSecret(secret)
			Expect(err).To(BeNil())
			Expect(config.Url).To(Equal("https://harbor.example.com"))
			Expect(config.Username).To(Equal("admin"))
			Expect(config.Password).To(Equal("password123"))
			Expect(config.AddressAlias).To(BeEmpty())
		})
	})

	Context("complete configuration test", func() {
		BeforeEach(func() {
			secret.Data = map[string][]byte{
				HarborConfigSecretKeyUrl:                []byte("https://harbor.example.com"),
				HarborConfigSecretKeyUsername:           []byte("admin"),
				HarborConfigSecretKeyPassword:           []byte("password123"),
				HarborConfigSecretKeyAddressAlias:       []byte("https://harbor-internal.local"),
				HarborConfigSecretKeyAliasPolicy:        []byte("IfIPEqual"),
				HarborConfigSecretKeyAliasCheckInterval: []byte("10m"),
			}
		})

		It("should correctly parse all configuration fields", func() {
			err := config.FromSecret(secret)
			Expect(err).To(BeNil())

			Expect(config.Url).To(Equal("https://harbor.example.com"))
			Expect(config.Username).To(Equal("admin"))
			Expect(config.Password).To(Equal("password123"))
			Expect(config.AddressAlias).To(Equal("https://harbor-internal.local"))
			Expect(config.AliasPolicy).To(Equal(HarborAddressAliasPolicyIfIPEqual))
			Expect(config.AliasCheckInterval).ToNot(BeNil())
			Expect(*config.AliasCheckInterval).To(Equal(10 * time.Minute))
		})
	})

	Context("address alias configuration", func() {
		Context("when AddressAlias is empty", func() {
			BeforeEach(func() {
				secret.Data = map[string][]byte{
					HarborConfigSecretKeyUrl:      []byte("https://harbor.example.com"),
					HarborConfigSecretKeyUsername: []byte("admin"),
					HarborConfigSecretKeyPassword: []byte("password123"),
				}
			})

			It("should not set alias related configurations", func() {
				err := config.FromSecret(secret)
				Expect(err).To(BeNil())
				Expect(config.AddressAlias).To(BeEmpty())
				Expect(config.AliasPolicy).To(BeEmpty())
				Expect(config.AliasCheckInterval).To(BeNil())
			})
		})

		Context("when AddressAlias is not empty", func() {
			BeforeEach(func() {
				secret.Data[HarborConfigSecretKeyUrl] = []byte("https://harbor.example.com")
				secret.Data[HarborConfigSecretKeyUsername] = []byte("admin")
				secret.Data[HarborConfigSecretKeyPassword] = []byte("password123")
				secret.Data[HarborConfigSecretKeyAddressAlias] = []byte("https://harbor-internal.local")
			})

			Context("with default policy and interval", func() {
				It("should set default alias policy and check interval", func() {
					err := config.FromSecret(secret)
					Expect(err).To(BeNil())
					Expect(config.AddressAlias).To(Equal("https://harbor-internal.local"))
					Expect(config.AliasPolicy).To(Equal(HarborAddressAliasPolicyIfIPEqual))
					Expect(config.AliasCheckInterval).ToNot(BeNil())
					Expect(*config.AliasCheckInterval).To(Equal(AliasCheckIntervalDefault))
				})
			})

			Context("with custom alias policy", func() {
				It("should correctly set Always policy", func() {
					secret.Data[HarborConfigSecretKeyAliasPolicy] = []byte("Always")

					err := config.FromSecret(secret)
					Expect(err).To(BeNil())
					Expect(config.AliasPolicy).To(Equal(HarborAddressAliasPolicyAlways))
				})

				It("should correctly set IfIPEqual policy", func() {
					secret.Data[HarborConfigSecretKeyAliasPolicy] = []byte("IfIPEqual")

					err := config.FromSecret(secret)
					Expect(err).To(BeNil())
					Expect(config.AliasPolicy).To(Equal(HarborAddressAliasPolicyIfIPEqual))
				})

				It("should use default policy when policy is empty", func() {
					secret.Data[HarborConfigSecretKeyAliasPolicy] = []byte("")

					err := config.FromSecret(secret)
					Expect(err).To(BeNil())
					Expect(config.AliasPolicy).To(Equal(HarborAddressAliasPolicyIfIPEqual))
				})
			})

			Context("with custom check interval", func() {
				It("should correctly parse valid time interval", func() {
					secret.Data[HarborConfigSecretKeyAliasCheckInterval] = []byte("5m")

					err := config.FromSecret(secret)
					Expect(err).To(BeNil())
					Expect(config.AliasCheckInterval).ToNot(BeNil())
					Expect(*config.AliasCheckInterval).To(Equal(5 * time.Minute))
				})

				It("should correctly parse complex time interval", func() {
					secret.Data[HarborConfigSecretKeyAliasCheckInterval] = []byte("1h30m")

					err := config.FromSecret(secret)
					Expect(err).To(BeNil())
					Expect(config.AliasCheckInterval).ToNot(BeNil())
					Expect(*config.AliasCheckInterval).To(Equal(1*time.Hour + 30*time.Minute))
				})

				It("should use default interval when interval is empty", func() {
					secret.Data[HarborConfigSecretKeyAliasCheckInterval] = []byte("")

					err := config.FromSecret(secret)
					Expect(err).To(BeNil())
					Expect(config.AliasCheckInterval).ToNot(BeNil())
					Expect(*config.AliasCheckInterval).To(Equal(AliasCheckIntervalDefault))
				})

				It("should return error when interval format is invalid", func() {
					secret.Data[HarborConfigSecretKeyAliasCheckInterval] = []byte("invalid-duration")

					err := config.FromSecret(secret)
					Expect(err).ToNot(BeNil())
					Expect(err.Error()).To(ContainSubstring("invalid duration"))
				})
			})
		})
	})

})
