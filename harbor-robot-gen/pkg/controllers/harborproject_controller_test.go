/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controllers

import (
	"testing"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestGetHarborProjectName(t *testing.T) {
	type testCase struct {
		name      string
		ns        *corev1.Namespace
		jsonPath  string
		expectVal string
		expectErr bool
	}

	namespace := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: "nsname",
			Labels: map[string]string{
				"project":          "project-1",
				"cpaas.io/project": "project-2",
				"empty":            "",
			},
		},
	}

	tests := []testCase{
		{
			name:      "valid jsonpath and value exists",
			ns:        namespace,
			jsonPath:  "{.metadata.labels.project}",
			expectVal: "project-1",
			expectErr: false,
		},
		{
			name:      "valid jsonpath and value exists, path contains dot",
			ns:        namespace,
			jsonPath:  `{.metadata.labels.cpaas\.io/project}`,
			expectVal: "project-2",
			expectErr: false,
		},
		{
			name:      "valid jsonpath and value exists, and complex expression",
			ns:        namespace,
			jsonPath:  `{.metadata.labels.cpaas\.io/project}-{.metadata.name}`,
			expectVal: "project-2-nsname",
			expectErr: false,
		},
		{
			name:      "invalid jsonpath",
			ns:        namespace,
			jsonPath:  "{.not.exist}",
			expectVal: "",
			expectErr: true,
		},
		{
			name:      "value is empty",
			ns:        namespace,
			jsonPath:  "{.metadata.labels.empty}",
			expectVal: "",
			expectErr: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			val, err := getHarborProjectName(tc.jsonPath, tc.ns)
			if tc.expectErr {
				if err == nil {
					t.Errorf("%s: expected error, got nil", tc.name)
				}
				if val != "" {
					t.Errorf("expected empty value, got %v", val)
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if val != tc.expectVal {
					t.Errorf("expected %v, got %v", tc.expectVal, val)
				}
			}
		})
	}
}
