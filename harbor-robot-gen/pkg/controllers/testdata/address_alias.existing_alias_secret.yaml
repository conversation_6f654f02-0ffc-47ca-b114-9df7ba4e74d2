apiVersion: v1
kind: Secret
metadata:
  name: test-image-pull-secret.alias
  namespace: test-namespace
  ownerReferences:
  - apiVersion: v1
    kind: Secret
    name: test-image-pull-secret
    controller: true
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ********************************************************************************************************************************************************************************
