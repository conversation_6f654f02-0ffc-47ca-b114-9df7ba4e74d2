apiVersion: v1
kind: Secret
metadata:
  name: test-image-pull-secret
  namespace: test-namespace
  labels:
    harbor-robot-gen.alaudadevops.alauda.io/generatedBy: HarborRobotBinding
    harbor-robot-gen.alaudadevops.alauda.io/harborRobotBinding: test-harbor-robot-binding
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ********************************************************************************************************************************************************************************************
