apiVersion: v1
kind: Secret
metadata:
  name: test-image-pull-secret-2.alias
  namespace: test-namespace
  ownerReferences:
  - apiVersion: v1
    kind: Secret
    name: test-image-pull-secret-2
    controller: true
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************************************************************************
