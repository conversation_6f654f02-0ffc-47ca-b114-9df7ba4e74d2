/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controllers

import (
	"context"
	"time"

	v1alpha1 "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/apis/v1alpha1"
	thirdpartyclient "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/mock/pkg/thirdpartyclient"
	harborClient "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/thirdpartyclient"
	goharbormodels "github.com/goharbor/go-client/pkg/sdk/v2.0/models"
	"github.com/golang/mock/gomock"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"go.uber.org/zap"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"knative.dev/pkg/logging"
)

var _ = PDescribe("HarborRobotBindingReconciler.syncHarborRobotBindingAccount", func() {
	var (
		r                *HarborRobotBindingReconciler
		mockCtrl         *gomock.Controller
		mockHarborClient *thirdpartyclient.MockHarborClient
		ctx              context.Context
		logger           *zap.SugaredLogger
		injection        *v1alpha1.HarborRobotBinding
		project          *goharbormodels.Project
		robot            *harborClient.HarborRobotBinding
	)

	BeforeEach(func() {
		mockCtrl = gomock.NewController(GinkgoT())
		mockHarborClient = thirdpartyclient.NewMockHarborClient(mockCtrl)
		log, _ := zap.NewProduction()
		logger = log.Sugar()
		ctx = logging.WithLogger(context.Background(), logger)
		ctx = context.WithValue(ctx, harborClientCtxKey{}, mockHarborClient)
		r = &HarborRobotBindingReconciler{
			NewHarborClient: func(url string, username string, password string) harborClient.HarborClient {
				return mockHarborClient
			},
		}

		injection = &v1alpha1.HarborRobotBinding{
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "test-namespace",
			},
			Spec: v1alpha1.HarborRobotBindingSpec{
				Harbor: v1alpha1.HarborSpec{
					Project: "test-project",
				},
				RefreshInterval: &metav1.Duration{Duration: 1 * time.Hour},
			},
		}
		injection.Status.InitializeConditions()

		project = &goharbormodels.Project{
			Name:      "test-project",
			ProjectID: 123,
		}

		robot = &harborClient.HarborRobotBinding{
			Name: "robot$test-project+test-namespace-injection",
			ID:   456,
		}
	})

	AfterEach(func() {
		mockCtrl.Finish()
	})

	When("robot account does not exist", func() {
		It("should create robot account successfully", func() {
			// Mock ListProjects
			mockHarborClient.EXPECT().
				ListProjects(gomock.Any()).
				Return([]*goharbormodels.Project{project}, nil)

			// Mock GetRobotAccounts
			mockHarborClient.EXPECT().
				GetRobotAccounts(gomock.Any(), int(project.ProjectID)).
				Return([]*harborClient.HarborRobotBinding{}, nil)

			// Mock CreateRobotAccount
			mockHarborClient.EXPECT().
				CreateRobotAccount(gomock.Any(), gomock.Any()).
				Return(robot, nil)

			// Call the function
			resultRobot, exists, projectID, err := r.syncHarborRobotAccount(ctx, injection)

			// Verify results
			Expect(err).To(BeNil())
			Expect(exists).To(BeFalse())
			Expect(projectID).To(Equal(int(project.ProjectID)))
			Expect(resultRobot).To(Equal(robot))
		})
	})

	When("robot account already exists", func() {
		It("should return existing robot account", func() {
			// Mock ListProjects
			mockHarborClient.EXPECT().
				ListProjects(gomock.Any()).
				Return([]*goharbormodels.Project{project}, nil)

			// Mock GetRobotAccounts with existing robot
			mockHarborClient.EXPECT().
				GetRobotAccounts(gomock.Any(), int(project.ProjectID)).
				Return([]*harborClient.HarborRobotBinding{robot}, nil)

			// Call the function
			resultRobot, exists, projectID, err := r.syncHarborRobotAccount(ctx, injection)

			// Verify results
			Expect(err).To(BeNil())
			Expect(exists).To(BeTrue())
			Expect(projectID).To(Equal(int(project.ProjectID)))
			Expect(resultRobot).To(Equal(robot))
		})
	})
})
