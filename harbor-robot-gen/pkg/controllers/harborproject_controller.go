/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controllers

import (
	"bytes"
	"context"
	goerrors "errors"
	"fmt"
	"time"

	v1alpha1 "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/apis/v1alpha1"
	"github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/thirdpartyclient"
	pkgctrl "github.com/AlaudaDevops/pkg/controllers"
	goharborProject "github.com/goharbor/go-client/pkg/sdk/v2.0/client/project"
	goharbormodels "github.com/goharbor/go-client/pkg/sdk/v2.0/models"
	"go.uber.org/zap"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/cli-runtime/pkg/printers"
	"k8s.io/client-go/tools/record"
	"knative.dev/pkg/logging"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

const (
	defaultRefreshInterval = 2 * time.Hour

	harborRobotGenSecretLabel         = "harbor-robot-gen" //nolint:gosec
	ProjectFieldJsonPathAnnotationKey = "harbor-robot-gen/projectFieldsPath"
)

// HarborProjectReconciler reconciles a HarborProject object
type HarborProjectReconciler struct {
	ctrlclient.Client

	SugaredLogger *zap.SugaredLogger
	Scheme        *runtime.Scheme
	Events        record.EventRecorder

	NewHarborClient func(url string, username string, password string) thirdpartyclient.HarborClient
}

func (r *HarborProjectReconciler) Reconcile(ctx context.Context, req ctrl.Request) (result ctrl.Result, err error) {
	log := logging.FromContext(ctx)
	log.Infow("reconciling harbor project", "key", req.NamespacedName)

	now := time.Now()
	defer func() {
		log.Infow("reconciled harbor project", "key", req.NamespacedName, "error", err, "elapsed", time.Since(now))
	}()

	// Get Secret
	secret := &corev1.Secret{}
	if err := r.Client.Get(ctx, req.NamespacedName, secret); err != nil {
		if errors.IsNotFound(err) {
			log.Debug("secret not found, ignoring")
			return ctrl.Result{}, nil
		}
		log.Errorw("failed to get secret", "error", err)
		return ctrl.Result{}, err
	}

	log = log.With("harbor", string(secret.Data["url"]))

	// Get Namespace
	nsList := corev1.NamespaceList{}
	if err := r.Client.List(ctx, &nsList); err != nil {
		log.Errorw("failed to list namespaces", "error", err)
		return ctrl.Result{}, err
	}

	projectNameJsonPath := ""
	if secret.Annotations != nil {
		if val, ok := secret.Annotations[ProjectFieldJsonPathAnnotationKey]; ok {
			projectNameJsonPath = val
		}
	}

	if projectNameJsonPath == "" {
		err = fmt.Errorf("annotation key '%s' should not be empty in secret %s/%s", ProjectFieldJsonPathAnnotationKey, secret.Namespace, secret.Name)
		return ctrl.Result{}, err
	}

	errs := []error{}

	for _, ns := range nsList.Items {

		// Check if namespace has required labels
		projectName, err := getHarborProjectName(projectNameJsonPath, &ns)
		if err != nil {
			log.Debugw("skip, cannot get harbor project name", "err", err)
			continue
		}

		if projectName == "" {
			log.Debug("skip, project name is empty")
			continue
		}

		// Ensure harbor project exists
		_, err = r.ensureHarborProject(ctx, secret, projectName)
		if err != nil {
			log.Errorw("failed to ensure harbor project", "error", err)
			errs = append(errs, err)
		}

		// // Ensure HarborRobotBinding exists
		// if err := r.ensureHarborRobotBinding(ctx, ns, projectName); err != nil {
		// 	log.Errorw("failed to ensure image pull secret injection", "error", err)
		// 	return ctrl.Result{}, err
		// }
	}

	if len(errs) > 0 {
		return ctrl.Result{}, goerrors.Join(errs...)
	}
	return ctrl.Result{}, nil
}

func (r *HarborProjectReconciler) ensureHarborProject(ctx context.Context, secret *corev1.Secret, projectName string) (*goharbormodels.Project, error) {
	log := logging.FromContext(ctx)
	log.Infow("ensuring harbor project exists", "project", projectName)

	// Get harbor client
	harborClient := r.buildHarborClient(ctx, secret)

	// List projects to check if project exists
	projects, err := harborClient.ListProjects(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list harbor projects: %w", err)
	}

	for _, project := range projects {
		if project.Name == projectName {
			log.Infow("harbor project already exists", "project", projectName)
			return project, nil
		}
	}

	// Create project if it doesn't exist
	log.Infow("creating harbor project", "project", projectName)
	projectCreate := goharborProject.CreateProjectParams{
		Project: &goharbormodels.ProjectReq{
			ProjectName: projectName,
			Metadata: &goharbormodels.ProjectMetadata{
				Public: "false",
			},
		},
	}

	project, err := harborClient.CreateProject(ctx, projectCreate)
	if err != nil {
		if goerrors.Is(err, goharborProject.NewCreateProjectConflict()) {
			return &goharbormodels.Project{
				Name: projectName,
			}, nil
		}
		return nil, fmt.Errorf("failed to create harbor project: %w", err)
	}

	log.Infow("created harbor project", "project", projectName)
	return project, nil
}

func (r *HarborProjectReconciler) ensureHarborRobotBinding(ctx context.Context, ns *corev1.Namespace, project *goharbormodels.Project, secret *corev1.Secret) error {
	log := logging.FromContext(ctx)
	log.Infow("ensuring image pull secret injection exists", "namespace", ns.Name)

	// Make desired HarborRobotBinding
	desired := makeHarborRobotBinding(ns, project, secret)

	// Check if HarborRobotBinding exists
	existing := &v1alpha1.HarborRobotBinding{}
	err := r.Client.Get(ctx, types.NamespacedName{Name: desired.Name, Namespace: desired.Namespace}, existing)

	if err != nil {
		if errors.IsNotFound(err) {
			// Create if not exists
			log.Infow("creating image pull secret injection", "name", desired.Name)
			if err := r.Client.Create(ctx, desired); err != nil {
				return fmt.Errorf("failed to create image pull secret injection: %w", err)
			}
			return nil
		}
		return fmt.Errorf("failed to get image pull secret injection: %w", err)
	}

	// Update if specs don't match
	if !imageInjectionSpecEqual(existing, desired) {
		log.Infow("updating image pull secret injection", "name", desired.Name)
		existing.Spec = desired.Spec
		if err := r.Client.Update(ctx, existing); err != nil {
			return fmt.Errorf("failed to update image pull secret injection: %w", err)
		}
	}

	return nil
}

func makeHarborRobotBinding(ns *corev1.Namespace, project *goharbormodels.Project, secret *corev1.Secret) *v1alpha1.HarborRobotBinding {
	injection := &v1alpha1.HarborRobotBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "default",
			Namespace: ns.Name,
		},
		Spec: v1alpha1.HarborRobotBindingSpec{
			Namespaces: v1alpha1.NamespaceSelectorSpec{
				Names: []string{ns.Name},
			},
			Harbor: v1alpha1.HarborSpec{
				Project: project.Name,
				Robot: v1alpha1.RobotSpec{
					Access: []v1alpha1.AccessSpec{
						{Action: "pull", Resource: "repository"},
					},
				},
				Secret: &corev1.SecretReference{
					Name:      secret.Name,
					Namespace: secret.Namespace,
				},
			},
			RefreshInterval: &metav1.Duration{Duration: defaultRefreshInterval},
			ServiceAccount: &v1alpha1.ServiceAccountBindingSpec{
				Name: "default",
			},
		},
	}

	return injection
}

func imageInjectionSpecEqual(a, b *v1alpha1.HarborRobotBinding) bool {
	return a.Spec.Harbor.Project == b.Spec.Harbor.Project &&
		a.Spec.RefreshInterval.Duration == b.Spec.RefreshInterval.Duration &&
		a.Spec.Harbor.Secret.Name == b.Spec.Harbor.Secret.Name &&
		a.Spec.Harbor.Secret.Namespace == b.Spec.Harbor.Secret.Namespace
}

func (r *HarborProjectReconciler) buildHarborClient(_ context.Context, secret *corev1.Secret) thirdpartyclient.HarborClient {
	url := string(secret.Data["url"])
	username := string(secret.Data["username"])
	password := string(secret.Data["password"])

	if r.NewHarborClient == nil {
		r.NewHarborClient = thirdpartyclient.NewHarborClient
	}

	return r.NewHarborClient(url, username, password)
}

// CheckSetup will Check whether the prerequisites are met
func (r *HarborProjectReconciler) CheckSetup(ctx context.Context, mgr ctrl.Manager, logger *zap.SugaredLogger) error {
	return nil
}

// Name return HarborRobotBinding name
func (r *HarborProjectReconciler) Name() string {
	return "harborproject-controller"
}

// Setup prepares and initializes the controller for usage
func (r *HarborProjectReconciler) Setup(ctx context.Context, mgr ctrl.Manager, logger *zap.SugaredLogger) error {
	r.SugaredLogger = logger
	r.Client = mgr.GetClient()
	r.Scheme = mgr.GetScheme()
	r.Events = mgr.GetEventRecorderFor(r.Name())
	if r.NewHarborClient == nil {
		r.NewHarborClient = thirdpartyclient.NewHarborClient
	}

	return r.SetupWithManager(mgr)
}

// SetupWithManager sets up the controller with the Manager.
func (r *HarborProjectReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		Named(r.Name()).
		WithOptions(pkgctrl.DefaultOptions()).
		For(&corev1.Secret{}, builder.WithPredicates(predicate.Funcs{
			CreateFunc: func(e event.CreateEvent) bool {
				return containsInMap(e.Object.GetLabels(), harborRobotGenSecretLabel)
			},
			UpdateFunc: func(e event.UpdateEvent) bool {
				return !containsInMap(e.ObjectOld.GetLabels(), harborRobotGenSecretLabel) && containsInMap(e.ObjectNew.GetLabels(), harborRobotGenSecretLabel)
			},
			DeleteFunc: func(e event.DeleteEvent) bool {
				return false
			},
			GenericFunc: func(e event.GenericEvent) bool {
				return false
			},
		})).
		Watches(
			&corev1.Namespace{},
			handler.EnqueueRequestsFromMapFunc(r.GetHarborRobotGenSecret),
			builder.WithPredicates(predicate.Or(
				pkgctrl.AnnotationChangedPredicate{},
				predicate.LabelChangedPredicate{},
			)),
		).
		WithOptions(pkgctrl.DefaultOptions()).
		Complete(r)
}

func containsInMap(m map[string]string, key string) bool {
	if len(m) == 0 {
		return false
	}
	_, ok := m[key]
	return ok
}

func (r *HarborProjectReconciler) GetHarborRobotGenSecret(ctx context.Context, obj ctrlclient.Object) []reconcile.Request {
	secretList := corev1.SecretList{}
	selector, err := metav1.LabelSelectorAsSelector(&metav1.LabelSelector{
		MatchLabels: map[string]string{harborRobotGenSecretLabel: "true"},
	})
	if err != nil {
		r.SugaredLogger.Errorw("failed to get label selector", "err", err)
		return nil
	}

	err = r.Client.List(ctx, &secretList, &ctrlclient.ListOptions{LabelSelector: selector})
	if err != nil {
		r.SugaredLogger.Errorw("failed to list secrets", "err", err)
		return nil
	}

	requests := make([]reconcile.Request, 0, len(secretList.Items))
	for _, secret := range secretList.Items {
		requests = append(requests, reconcile.Request{
			NamespacedName: types.NamespacedName{
				Namespace: secret.Namespace,
				Name:      secret.Name,
			},
		})
	}

	return requests
}

// getHarborProjectName returns the name of the harbor project based on the namespace's annotation with the given jsonpath.
// the jsonpath format is same as https://kubernetes.io/docs/reference/kubectl/jsonpath/
func getHarborProjectName(projectFieldsJsonPath string, namespace *corev1.Namespace) (string, error) {

	p, err := printers.NewJSONPathPrinter(projectFieldsJsonPath)
	if err != nil {
		return "", err
	}
	strBuffer := bytes.NewBufferString("")
	err = p.PrintObj(namespace, strBuffer)
	if err != nil {
		return "", err
	}

	return strBuffer.String(), nil
}
