/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controllers

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"reflect"
	"sort"
	"time"

	thirdpartyclient "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/thirdpartyclient"
	kclient "github.com/AlaudaDevops/pkg/client"
	pkgctrl "github.com/AlaudaDevops/pkg/controllers"
	goharborRobot "github.com/goharbor/go-client/pkg/sdk/v2.0/client/robot"
	goharbormodels "github.com/goharbor/go-client/pkg/sdk/v2.0/models"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	"knative.dev/pkg/system"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	"go.uber.org/zap"
	"knative.dev/pkg/logging"

	v1alpha1 "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/apis/v1alpha1"
	secretpkg "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/secret"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
)

var (
	HarborRobotBindingFinalizer        = v1alpha1.GroupVersion.Group + "/finalizer"
	LabelGeneratedBy                   = v1alpha1.GroupVersion.Group + "/generatedBy"
	ValueGeneratedByHarborRobotBinding = "HarborRobotBinding"
	HarborRobotBindingLabelKey         = v1alpha1.GroupVersion.Group + "/harborRobotBinding"
	nextRefreshTimeKey                 = "nextRefreshTime"
	lastRefreshTimeKey                 = "lastRefreshTime"
)

// HarborRobotBindingReconciler reconciles an HarborRobotBindingReconciler object
type HarborRobotBindingReconciler struct {
	ctrlclient.Client

	SugaredLogger *zap.SugaredLogger
	Scheme        *runtime.Scheme
	Events        record.EventRecorder

	NewHarborClient func(url string, username string, password string) thirdpartyclient.HarborClient

	httpClient *http.Client
}

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// it will sync connectorclass and secret, then doing liveness check and auth check
func (r *HarborRobotBindingReconciler) Reconcile(ctx context.Context, req ctrl.Request) (result ctrl.Result, err error) {

	begin := time.Now()
	log := r.SugaredLogger.With("key", req)
	ctx = logging.WithLogger(ctx, log)
	log.Info("Reconciling")

	obj := &v1alpha1.HarborRobotBinding{}
	if err = r.Client.Get(ctx, req.NamespacedName, obj); err != nil {
		err = ctrlclient.IgnoreNotFound(err)
		return ctrl.Result{}, err
	}
	ctx = logging.WithLogger(ctx, log)

	harborClient, err := r.buildHarborClient(ctx, obj)
	if err != nil {
		log.Errorw("build harbor client error", "err", err)
		return ctrl.Result{}, err
	}
	ctx = harborClientWithContext(ctx, harborClient)

	oldObj := obj.DeepCopy()
	obj.Status.InitializeConditions()

	if !controllerutil.ContainsFinalizer(obj, HarborRobotBindingFinalizer) {
		patch := ctrlclient.MergeFrom(oldObj)
		controllerutil.AddFinalizer(obj, HarborRobotBindingFinalizer)
		if err = r.Patch(ctx, obj, patch); err != nil {
			log.Errorw("patch error when adding finalizer")
			return
		}
	}

	defer func() {
		elapsed := time.Since(begin)
		log.Infow("Reconciled", "elapsed", elapsed.String(), "err", err)
	}()

	defer func() {

		if obj.Status.ObservedGeneration == obj.Generation && reflect.DeepEqual(oldObj.Status, obj.Status) {
			return
		}

		obj.Status.ObservedGeneration = obj.Generation

		patch := ctrlclient.MergeFrom(oldObj)
		patchData, _ := patch.Data(obj)
		if len(patchData) <= 2 {
			return
		}

		log.Debugw("patching harborrobotbinding status", "patch-data", string(patchData), "old", oldObj.Status, "new", obj.Status)

		patchErr := r.Client.Status().Update(ctx, obj)
		if patchErr != nil {
			log.Errorw("patch harborrobotbinding status error", "err", patchErr, "patch-data", string(patchData), "elapsed", time.Since(begin))
			err = patchErr
		} else {
			log.Infow("harborrobotbinding status patched", "patch-data", string(patchData))
		}

		log.Infow("will requeue ", "requeue-after", result.RequeueAfter.String(), "requeue", result.Requeue, "next-refresh-time", obj.Status.NextRefreshTime)

	}()

	if obj.DeletionTimestamp != nil {
		if len(obj.Finalizers) != 0 {
			log.Info("deletionTimestamp is not nil, wait release finalizers")
			err = r.finalize(ctx, obj)
			return ctrl.Result{}, err
		}
		return ctrl.Result{}, nil
	}

	err = r.reconcile(ctx, obj)
	if err != nil {
		return ctrl.Result{}, err
	}

	after := time.Until(obj.Status.NextRefreshTime.Time)
	if after.Milliseconds() < 0 {
		result.Requeue = true
	} else {
		result.RequeueAfter = after
	}

	return result, nil
}

func (r *HarborRobotBindingReconciler) finalize(ctx context.Context, obj *v1alpha1.HarborRobotBinding) error {
	log := logging.FromContext(ctx)

	errs := []error{}

	// 1. Delete Harbor robot account
	if err := r.deleteHarborRobotAccount(ctx, obj); err != nil {
		log.Errorw("failed to delete harbor robot account", "err", err)
		errs = append(errs, err)
	}

	// 2. Delete image pull secret and remove it from service account
	if obj.Spec.ServiceAccount != nil {
		if err := r.removeAllGeneratedImagePullSecrets(ctx, obj); err != nil {
			log.Errorw("failed to delete image pull secret", "err", err)
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		return errors.Join(errs...)
	}

	// 3. Remove finalizer
	controllerutil.RemoveFinalizer(obj, HarborRobotBindingFinalizer)
	if err := r.Update(ctx, obj); err != nil {
		log.Errorw("failed to remove finalizer", "err", err)
		return err
	}

	return nil
}

func (r *HarborRobotBindingReconciler) deleteHarborRobotAccount(ctx context.Context, obj *v1alpha1.HarborRobotBinding) error {
	log := logging.FromContext(ctx)

	harborClient := harborClientFromContext(ctx)
	if harborClient == nil {
		return fmt.Errorf("harbor client not found in context")
	}

	// Get project ID
	projects, err := harborClient.ListProjects(ctx)
	if err != nil {
		log.Errorw("failed to list harbor projects", "err", err)
		return err
	}

	var harborProjectID int
	for _, project := range projects {
		if project.Name == obj.Spec.Harbor.Project {
			harborProjectID = int(project.ProjectID)
			break
		}
	}

	if harborProjectID == 0 {
		log.Info("harbor project not found, skipping robot account deletion")
		return nil
	}

	// Find and delete robot account
	robots, err := harborClient.GetRobotAccounts(ctx, harborProjectID)
	if err != nil {
		log.Errorw("failed to get robot accounts", "err", err)
		return err
	}

	proposalRobotName := genProposalRobotName(obj)
	for _, robot := range robots {
		if robot.Name == fmt.Sprintf("robot$%s+%s", obj.Spec.Harbor.Project, proposalRobotName) {
			if err := harborClient.DeleteRobotAccount(ctx, harborProjectID, int(robot.ID)); err != nil {
				log.Errorw("failed to delete harbor robot account", "err", err)
				return err
			}

			log.Infow("deleted harbor robot account", "robot-name", robot.Name)
			break
		}
	}

	return nil
}

func (r *HarborRobotBindingReconciler) removeAllGeneratedImagePullSecrets(ctx context.Context, harborRobotBinding *v1alpha1.HarborRobotBinding) error {
	existingNamespaces, err := r.listExistingSecretNamespaces(ctx, harborRobotBinding)
	if err != nil {
		return fmt.Errorf("failed to get existing secret namespaces: %w", err)
	}

	for _, namespace := range existingNamespaces {
		if err := r.removeImagePullSecret(ctx, harborRobotBinding, namespace); err != nil {
			return err
		}
	}

	return nil
}

type harborClientCtxKey struct{}

func harborClientFromContext(ctx context.Context) thirdpartyclient.HarborClient {
	val := ctx.Value(harborClientCtxKey{})
	if val == nil {
		return nil
	}

	return val.(thirdpartyclient.HarborClient)
}

func harborClientWithContext(ctx context.Context, harborClient thirdpartyclient.HarborClient) context.Context {
	return context.WithValue(ctx, harborClientCtxKey{}, harborClient)
}

func (r *HarborRobotBindingReconciler) reconcile(ctx context.Context, harborRobotBinding *v1alpha1.HarborRobotBinding) error {
	log := logging.FromContext(ctx).With("harbor-project", harborRobotBinding.Spec.Harbor.Project)

	harborClient := harborClientFromContext(ctx)
	if harborClient == nil {
		return fmt.Errorf("harbor client not found in context")
	}

	robot, robotAlreadyExists, harborProjectID, err := r.syncHarborRobotAccount(ctx, harborRobotBinding)
	if err != nil {
		log.Errorw("sync harbor robot account error", "err", err)
		harborRobotBinding.Status.SetCondition(v1alpha1.HarborRobotAccountCondition, err, "SyncHarborAccountError")
		return err
	}
	log.Infow("synced harbor robot account", "robot-already-exists", robotAlreadyExists, "robot", robot.Name)
	harborRobotBinding.Status.SetCondition(v1alpha1.HarborRobotAccountCondition, nil, "")

	token, skipRefresh, err := r.refreshHarborRobotAccount(ctx, harborRobotBinding, robot, harborProjectID)
	if err != nil {
		log.Errorw("refresh harbor robot account error", "err", err)
		harborRobotBinding.Status.SetCondition(v1alpha1.RobotSecretCondition, err, "RefreshRobotAccountSecretError")
		return err
	}
	if !skipRefresh {
		robot.Secret = token
	}

	robotSecret, err := r.syncRobotSecret(ctx, harborRobotBinding, robot, skipRefresh)
	if err != nil {
		log.Errorw("sync robot secret error", "err", err)
		harborRobotBinding.Status.SetCondition(v1alpha1.RobotSecretCondition, err, "RobotSecretError")
		return err
	}
	harborRobotBinding.Status.SetCondition(v1alpha1.RobotSecretCondition, nil, "")

	err = r.syncAllImagePullSecret(ctx, harborRobotBinding, robot, string(robotSecret.Data["token"]))
	if err != nil {
		log.Errorw("sync image pull secret error", "err", err)
		harborRobotBinding.Status.SetCondition(v1alpha1.SecretSyncConditionSecret, err, "SyncSecretError")
		return err
	}
	harborRobotBinding.Status.SetCondition(v1alpha1.SecretSyncConditionSecret, nil, "")

	return nil
}

func (r *HarborRobotBindingReconciler) syncHarborRobotAccount(ctx context.Context, harborRobotBinding *v1alpha1.HarborRobotBinding) (
	robot *thirdpartyclient.HarborRobotBinding, alreadyExists bool, harborProjectID int, err error) {
	harborClient := harborClientFromContext(ctx)
	if harborClient == nil {
		return nil, false, 0, fmt.Errorf("harbor client not found")
	}

	robot, harborProjectID, alreadyExists, err = r.createRobotAccountIfNotExists(ctx, harborClient, harborRobotBinding)
	if err != nil {
		return nil, alreadyExists, harborProjectID, err
	}

	if harborRobotBinding.Status.LastRefreshTime == nil {
		harborRobotBinding.Status.LastRefreshTime = &metav1.Time{Time: time.Now()}
	}

	if harborRobotBinding.Status.NextRefreshTime == nil {
		harborRobotBinding.Status.NextRefreshTime = &metav1.Time{Time: time.Now().Add(harborRobotBinding.Spec.RefreshInterval.Duration)}
	}

	return robot, alreadyExists, harborProjectID, nil
}

func (r *HarborRobotBindingReconciler) buildHarborClient(ctx context.Context, harborRobotBinding *v1alpha1.HarborRobotBinding) (thirdpartyclient.HarborClient, error) {
	log := logging.FromContext(ctx)

	if harborRobotBinding.Spec.Harbor.Secret == nil {
		log.Error("harbor secret is required")
		return nil, fmt.Errorf("harbor secret is required")
	}

	secret := &corev1.Secret{}
	if err := r.Client.Get(ctx, types.NamespacedName{Namespace: harborRobotBinding.Spec.Harbor.Secret.Namespace, Name: harborRobotBinding.Spec.Harbor.Secret.Name}, secret); err != nil {
		log.Errorw("get harbor secret error", "err", err)
		return nil, err
	}

	harborURL := string(secret.Data["url"])
	username := string(secret.Data["username"])
	password := string(secret.Data["password"])

	return thirdpartyclient.NewHarborClient(harborURL, username, password), nil
}

func (r *HarborRobotBindingReconciler) createRobotAccountIfNotExists(ctx context.Context, harborClient thirdpartyclient.HarborClient, harborRobotBinding *v1alpha1.HarborRobotBinding) (
	robot *thirdpartyclient.HarborRobotBinding, harborProjectID int, alreadyExists bool, err error) {
	log := logging.FromContext(ctx).With("harbor-project", harborRobotBinding.Spec.Harbor.Project)

	projects, err := harborClient.ListProjects(ctx)
	if err != nil {
		log.Errorw("list harbor projects error", "err", err)
		return nil, 0, false, err
	}
	for _, project := range projects {
		if project.Name == harborRobotBinding.Spec.Harbor.Project {
			harborProjectID = int(project.ProjectID)
			break
		}
	}

	if harborProjectID == 0 {
		log.Errorw("harbor project not found", "project", harborRobotBinding.Spec.Harbor.Project)
		return nil, 0, false, fmt.Errorf("harbor project not found: %s", harborRobotBinding.Spec.Harbor.Project)
	}

	robots, err := harborClient.GetRobotAccounts(ctx, harborProjectID)
	if err != nil {
		log.Errorw("get harbor robot accounts error", "err", err)
		return nil, 0, false, err
	}

	proposalRobotName := genProposalRobotName(harborRobotBinding)
	for _, robot := range robots {
		if robot.Name == fmt.Sprintf("robot$%s+%s", harborRobotBinding.Spec.Harbor.Project, proposalRobotName) {
			log.Infow("harbor robot account already exists", "robot-name", proposalRobotName, "robot-id", robot.ID)
			return robot, harborProjectID, true, nil
		}
	}

	log.Debugw("harbor robot account does not exist", "robot-name", proposalRobotName)

	robotWillCreate := r.makeRobotAccount(harborRobotBinding)
	robotCreated, err := harborClient.CreateRobotAccount(ctx, robotWillCreate)
	if err != nil {
		log.Errorw("create harbor robot account error", "err", err)
		return nil, 0, false, err
	}
	log.Infow("harbor robot account created", "name", proposalRobotName)

	return robotCreated, harborProjectID, false, nil
}

func genProposalRobotName(robotBinding *v1alpha1.HarborRobotBinding) string {
	name := robotBinding.Spec.Harbor.Robot.Name
	if name == "" {
		name = robotBinding.Name
	}
	return fmt.Sprintf("%s-%s", name, string(robotBinding.UID))
}

func (r *HarborRobotBindingReconciler) makeRobotAccount(robotBinding *v1alpha1.HarborRobotBinding) goharbormodels.RobotCreate {

	permission := &goharbormodels.RobotPermission{
		Access:    []*goharbormodels.Access{},
		Kind:      "project",
		Namespace: robotBinding.Spec.Harbor.Project,
	}
	for _, item := range robotBinding.Spec.Harbor.Robot.Access {
		access := goharbormodels.Access{
			Resource: item.Resource,
			Action:   item.Action,
		}
		permission.Access = append(permission.Access, &access)
	}

	return goharbormodels.RobotCreate{
		Name:        genProposalRobotName(robotBinding),
		Description: fmt.Sprintf("Robot account for HarborRobotBinding %s, UID %s", robotBinding.Name, robotBinding.UID),
		Level:       "project",
		Permissions: []*goharbormodels.RobotPermission{permission},
		Duration:    -1,
	}
}

func (r *HarborRobotBindingReconciler) refreshHarborRobotAccount(ctx context.Context, harborRobotBinding *v1alpha1.HarborRobotBinding, robot *thirdpartyclient.HarborRobotBinding, harborProjectID int) (
	token string, skipRefresh bool, err error) {
	log := logging.FromContext(ctx).With("harbor-project", harborRobotBinding.Spec.Harbor.Project).With("robot", robot.Name)

	if harborRobotBinding.Status.NextRefreshTime != nil && harborRobotBinding.Status.NextRefreshTime.Time.After(time.Now()) {
		log.Infow("no need to refresh harbor robot account", "nextRefreshTime", harborRobotBinding.Status.NextRefreshTime)
		recaculatedNextRefreshTime := metav1.Time{Time: harborRobotBinding.Status.LastRefreshTime.Time.Add(harborRobotBinding.Spec.RefreshInterval.Duration)}
		if !recaculatedNextRefreshTime.Time.Equal(harborRobotBinding.Status.NextRefreshTime.Time) {
			log.Infow("refresh interval may be changed, re-calculate next refresh time", "lastRefreshTime", harborRobotBinding.Status.LastRefreshTime, "nextRefreshTime", harborRobotBinding.Status.NextRefreshTime, "recalculate", recaculatedNextRefreshTime)
			harborRobotBinding.Status.NextRefreshTime = &recaculatedNextRefreshTime
		}
		return "", true, nil
	}

	harborClient := harborClientFromContext(ctx)
	if harborClient == nil {
		return "", false, fmt.Errorf("failed to get harbor client from context")
	}

	if robot.ID == 0 {
		return "", false, fmt.Errorf("robot account ID is 0")
	}

	refreshParams := goharborRobot.RefreshSecParams{
		RobotID: robot.ID,
	}

	log.Debug("refreshing harbor robot account")
	token, err = harborClient.RefreshRobotAccount(ctx, harborProjectID, int(robot.ID), refreshParams)
	if err != nil {
		log.Errorw("Failed to refresh robot account", "err", err)
		return "", false, err
	}

	// Update the last refresh time
	lastRefreshTime := metav1.Now()
	harborRobotBinding.Status.LastRefreshTime = &lastRefreshTime
	harborRobotBinding.Status.NextRefreshTime = &metav1.Time{Time: lastRefreshTime.Add(harborRobotBinding.Spec.RefreshInterval.Duration)}

	log.Infof("refreshed harbor robot account")
	return token, false, nil
}

func (r *HarborRobotBindingReconciler) recreateRobotAccount(ctx context.Context, harborRobotBinding *v1alpha1.HarborRobotBinding, robot *thirdpartyclient.HarborRobotBinding) (
	*thirdpartyclient.HarborRobotBinding, error) {
	log := logging.FromContext(ctx)
	harborClient := harborClientFromContext(ctx)
	if harborClient == nil {
		return nil, fmt.Errorf("failed to get harbor client from context")
	}

	// Delete existing robot account
	if err := harborClient.DeleteRobotAccount(ctx, int(robot.ID), int(robot.ID)); err != nil {
		log.Errorw("failed to delete robot account", "err", err)
		return nil, err
	}

	// Create new robot account
	robotWillCreate := r.makeRobotAccount(harborRobotBinding)
	newRobot, err := harborClient.CreateRobotAccount(ctx, robotWillCreate)
	if err != nil {
		log.Errorw("failed to create robot account", "err", err)
		return nil, err
	}

	return newRobot, nil
}

func (r *HarborRobotBindingReconciler) syncRobotSecret(ctx context.Context, harborRobotBinding *v1alpha1.HarborRobotBinding, robot *thirdpartyclient.HarborRobotBinding, skipRefresh bool) (*corev1.Secret, error) {
	log := logging.FromContext(ctx)
	refreshed := !skipRefresh
	if refreshed && robot.Secret == "" {
		log.Errorw("it should not happen", errors.New("last refreshed robot account, but robot account token is empty"))
		return nil, fmt.Errorf("failed to refresh robot account: secret is empty")
	}

	harborClient := harborClientFromContext(ctx)
	if harborClient == nil {
		log.Errorw("failed to get harbor client from context")
		return nil, fmt.Errorf("failed to get harbor client from context")
	}

	baseURL := harborClient.GetBaseUrl()
	secret := MakeRobotSecret(harborRobotBinding, robot.Name, baseURL, robot.Secret)

	// Check if secret exists
	existingSecret := &corev1.Secret{}
	err := r.Client.Get(ctx, types.NamespacedName{Namespace: secret.Namespace, Name: secret.Name}, existingSecret)

	if err == nil {

		shouldUpdate := false
		if refreshed && !reflect.DeepEqual(existingSecret.Data, secret.Data) {
			existingSecret.Data = secret.Data
			existingSecret.Annotations = secret.Annotations
			existingSecret.Labels = secret.Labels
			shouldUpdate = true
		}
		// next refresh time may be changed caused by spec.refreshInterval, so the nextRefreshTime is recaculated in refreshRobotAccount and not really refreshed token
		if existingSecret.Annotations[nextRefreshTimeKey] != secret.Annotations[nextRefreshTimeKey] {
			existingSecret.Annotations = secret.Annotations
			shouldUpdate = true
		}

		if shouldUpdate {
			if err := r.Client.Update(ctx, existingSecret); err != nil {
				return nil, fmt.Errorf("failed to update secret: %w", err)
			}
			log.Infow("updated robot secret", "name", secret.Name)

			if refreshed {
				r.Events.Eventf(harborRobotBinding, corev1.EventTypeNormal, "Refreshed", "Refreshed robot secret, next refresh time: %s", harborRobotBinding.Status.NextRefreshTime)
			}

			return existingSecret, nil
		}

		log.Debugw("nothing changed, skip updating robot secret", "name", secret.Name, "refreshed", refreshed)
		return existingSecret, nil
	}

	if err != nil && !apierrors.IsNotFound(err) {
		return nil, fmt.Errorf("failed to get secret: %w", err)
	}

	// not found the secret in k8s, should create it
	// when robot account is already exists, and we do not need to refresh token, that we cannot get token from harbor, so we should just recreate it
	if robot.Secret == "" {
		log.Info("token is empty, and secret is not exists in k8s, we will recreate robot account", "robot", robot.Name)

		newRobot, err := r.recreateRobotAccount(ctx, harborRobotBinding, robot)
		if err != nil {
			return nil, fmt.Errorf("failed to recreate robot account: %w", err)
		}
		robot = newRobot
		secret = MakeRobotSecret(harborRobotBinding, robot.Name, baseURL, robot.Secret)
	}

	if err := r.Client.Create(ctx, &secret); err != nil {
		return nil, fmt.Errorf("failed to create robot secret: %w", err)
	}

	r.Events.Eventf(harborRobotBinding, corev1.EventTypeNormal, "Created", "Created robot secret, next refresh time: %s", harborRobotBinding.Status.NextRefreshTime)
	log.Infow("created new robot secret", "secret-name", secret.Name)
	return &secret, nil
}

func (r *HarborRobotBindingReconciler) syncAllImagePullSecret(ctx context.Context, harborRobotBinding *v1alpha1.HarborRobotBinding, robot *thirdpartyclient.HarborRobotBinding, robotToken string) error {
	log := logging.FromContext(ctx)

	desiredNamespaces, err := r.listDesiredNamespaces(ctx, harborRobotBinding)
	if err != nil {
		return fmt.Errorf("failed to get desired namespaces: %w", err)
	}
	log.Infow("got desired namespaces", "count", len(desiredNamespaces))

	existingNamespaces, err := r.listExistingSecretNamespaces(ctx, harborRobotBinding)
	if err != nil {
		return fmt.Errorf("failed to get existing secret namespaces: %w", err)
	}
	log.Infow("got existing secret namespaces", "count", len(existingNamespaces))

	// Get diff result
	undesiredNS, toUpdate, toAdd := diffNamespaces(desiredNamespaces, existingNamespaces)

	errs := []error{}

	for _, ns := range undesiredNS {
		log.Infow("removing image pull secret in undesired namespace", "namespace", ns)
		if err := r.removeImagePullSecret(ctx, harborRobotBinding, ns); err != nil {
			log.Errorw("failed to remove image pull secret in namespace", "namespace", ns, "err", err)
			errs = append(errs, fmt.Errorf("failed to remove secret in namespace %s, err: %s", ns, err.Error()))
			continue
		}
	}

	for _, ns := range append(toUpdate, toAdd...) {
		log.Infow("create or update image pull secret in namespace", "namespace", ns)
		secret, err := r.upsertImagePullSecret(ctx, ns, harborRobotBinding, robot, robotToken)
		if err != nil {
			log.Errorw("failed to create or update image pull secret in namespace", "namespace", ns, "err", err)
			errs = append(errs, fmt.Errorf("failed to create or update secret in namespace %s, err: %s", ns, err.Error()))
			continue
		}

		if err := r.updateServiceAccountInNamespace(ctx, ns, harborRobotBinding, secret); err != nil {
			log.Errorw("failed to ensure service account in namespace", "namespace", ns, "err", err)
			errs = append(errs, fmt.Errorf("failed to ensure service account in namespace %s, err: %s", ns, err.Error()))
			continue
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("failed to ensure image pull secret: %s", errors.Join(errs...).Error())
	}

	return nil
}

// listDesiredNamespaces returns list of namespaces selected by spec.namespaces
func (r *HarborRobotBindingReconciler) listDesiredNamespaces(ctx context.Context, harborRobotBinding *v1alpha1.HarborRobotBinding) ([]string, error) {
	log := logging.FromContext(ctx)

	// Create a map to store selected namespaces
	selectedNs := make(map[string]struct{})

	// Add namespaces from explicit names list
	if len(harborRobotBinding.Spec.Namespaces.Names) > 0 {
		for _, name := range harborRobotBinding.Spec.Namespaces.Names {
			selectedNs[name] = struct{}{}
		}
		log.Debugw("selected namespaces by names", "count", len(selectedNs))
	}

	// Add namespaces matching the label selector
	if harborRobotBinding.Spec.Namespaces.Selector != nil {
		selector, err := metav1.LabelSelectorAsSelector(harborRobotBinding.Spec.Namespaces.Selector)
		if err != nil {
			return nil, fmt.Errorf("invalid label selector: %w", err)
		}

		namespaceList := &corev1.NamespaceList{}
		if err := r.Client.List(ctx, namespaceList, &ctrlclient.ListOptions{LabelSelector: selector}); err != nil {
			return nil, fmt.Errorf("failed to list namespaces: %w", err)
		}

		for _, ns := range namespaceList.Items {
			selectedNs[ns.Name] = struct{}{}
		}
	}

	result := make([]string, 0, len(selectedNs))
	for ns := range selectedNs {
		result = append(result, ns)
	}

	sort.Strings(result) // Sort for consistent order
	return result, nil
}

func secretLabelsGenerated(harborRobotBinding *v1alpha1.HarborRobotBinding) map[string]string {
	return map[string]string{
		LabelGeneratedBy:           ValueGeneratedByHarborRobotBinding,
		HarborRobotBindingLabelKey: harborRobotBinding.Name,
	}
}

// listExistingSecretNamespaces returns list of namespaces containing our generated secrets
func (r *HarborRobotBindingReconciler) listExistingSecretNamespaces(ctx context.Context, harborRobotBinding *v1alpha1.HarborRobotBinding) ([]string, error) {
	secretList := &corev1.SecretList{}
	labelSelector := ctrlclient.MatchingLabels(secretLabelsGenerated(harborRobotBinding))

	if err := r.Client.List(ctx, secretList, labelSelector); err != nil {
		return nil, err
	}

	// Extract unique namespaces
	nsMap := make(map[string]struct{})
	for _, secret := range secretList.Items {
		nsMap[secret.Namespace] = struct{}{}
	}

	namespaces := make([]string, 0, len(nsMap))
	for ns := range nsMap {
		namespaces = append(namespaces, ns)
	}
	return namespaces, nil
}

// diffNamespaces compares desired and existing namespaces and returns lists for different operations
func diffNamespaces(desired, existing []string) (undesiredNS, toUpdate, toAdd []string) {
	desiredSet := stringSliceToSet(desired)
	existingSet := stringSliceToSet(existing)

	undesiredNS = make([]string, 0)
	toUpdate = make([]string, 0)
	toAdd = make([]string, 0)

	// Find namespaces to delete (in existing but not in desired)
	for ns := range existingSet {
		if _, ok := desiredSet[ns]; !ok {
			undesiredNS = append(undesiredNS, ns)
		}
	}

	// Find namespaces to update (in both lists)
	for ns := range existingSet {
		if _, ok := desiredSet[ns]; ok {
			toUpdate = append(toUpdate, ns)
		}
	}

	// Find namespaces to add (in desired but not in existing)
	for ns := range desiredSet {
		if _, ok := existingSet[ns]; !ok {
			toAdd = append(toAdd, ns)
		}
	}

	// Sort for consistent order
	sort.Strings(undesiredNS)
	sort.Strings(toUpdate)
	sort.Strings(toAdd)

	return undesiredNS, toUpdate, toAdd
}

// removeImagePullSecret removes the generated secret from the specified namespace
func (r *HarborRobotBindingReconciler) removeImagePullSecret(ctx context.Context, harborRobotBinding *v1alpha1.HarborRobotBinding, namespace string) error {
	log := logging.FromContext(ctx)

	secretList := &corev1.SecretList{}
	labelSelector := ctrlclient.MatchingLabels(secretLabelsGenerated(harborRobotBinding))
	if err := r.Client.List(ctx, secretList, ctrlclient.InNamespace(namespace), labelSelector); err != nil {
		return err
	}

	for i := range secretList.Items {

		log.Debugw("removing image pull secret", "namespace", namespace, "name", secretList.Items[i].Name)
		if err := r.removePullSecretInServiceAccount(ctx, namespace, secretList.Items[i].Name, harborRobotBinding); err != nil {
			return err
		}

		if err := r.Client.Delete(ctx, &secretList.Items[i]); err != nil && !apierrors.IsNotFound(err) {
			return err
		}

		log.Info("removed image pull secret", "namespace", namespace, "name", secretList.Items[i].Name)
	}

	return nil
}

func (r *HarborRobotBindingReconciler) removePullSecretInServiceAccount(ctx context.Context, namespace, secretName string, harborRobotBinding *v1alpha1.HarborRobotBinding) error {

	if harborRobotBinding.Spec.ServiceAccount == nil {
		return nil
	}

	log := logging.FromContext(ctx)
	saName := harborRobotBinding.Spec.ServiceAccount.Name

	sa := &corev1.ServiceAccount{}
	err := r.Client.Get(ctx, types.NamespacedName{Namespace: namespace, Name: saName}, sa)
	if err != nil {
		return err
	}

	changed := secretpkg.RemoveImagePullSecretFromServiceAccount(sa, secretName)

	if !changed {
		log.Debugw("no change, skip updating service account", "namespace", namespace, "name", saName)
		return nil
	}

	if err := r.Client.Update(ctx, sa); err != nil {
		log.Errorw("failed to update service account", "namespace", namespace, "name", saName, "err", err)
		return err
	}
	log.Infof("removed image pull secret %s in service account %s/%s", secretName, namespace, saName)

	return nil
}

// Helper function to convert string slice to set
func stringSliceToSet(slice []string) map[string]struct{} {
	set := make(map[string]struct{}, len(slice))
	for _, s := range slice {
		set[s] = struct{}{}
	}
	return set
}

func (r *HarborRobotBindingReconciler) upsertImagePullSecret(ctx context.Context, namespace string, harborRobotBinding *v1alpha1.HarborRobotBinding, robot *thirdpartyclient.HarborRobotBinding, robotToken string) (*corev1.Secret, error) {
	log := logging.FromContext(ctx)

	harborClient := harborClientFromContext(ctx)
	if harborClient == nil {
		log.Errorw("failed to get harbor client from context")
		return nil, fmt.Errorf("failed to get harbor client from context")
	}

	secret := MakeImagePullSecret(namespace, harborRobotBinding, robot.Name, harborClient.GetBaseUrl(), robotToken)

	// Check if secret exists
	existingSecret := &corev1.Secret{}
	err := r.Client.Get(ctx, types.NamespacedName{Namespace: namespace, Name: secret.Name}, existingSecret)

	if apierrors.IsNotFound(err) {
		if err := r.Client.Create(ctx, &secret); err != nil {
			return nil, fmt.Errorf("failed to create image pull secret: %w", err)
		}
		log.Infow("created new image pull secret", "secret", secret.Namespace+"/"+secret.Name)
		r.Events.Eventf(&secret, corev1.EventTypeNormal, "Created", "Created secret caused by HarborRobotBinding: '%s', next refresh time: '%s'", harborRobotBinding.Name, harborRobotBinding.Status.NextRefreshTime)
		return &secret, nil
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get secret: %w", err)
	}

	// err is  nil, Compare and update if needed
	if !reflect.DeepEqual(existingSecret.Data, secret.Data) || existingSecret.Annotations[nextRefreshTimeKey] != secret.Annotations[nextRefreshTimeKey] {
		existingSecret.Data = secret.Data
		existingSecret.Annotations = secret.Annotations
		if err := r.Client.Update(ctx, existingSecret); err != nil {
			return nil, fmt.Errorf("failed to update secret: %w", err)
		}
		log.Infow("updated image pull secret", "name", secret.Name)
		r.Events.Eventf(existingSecret, corev1.EventTypeNormal, "Refreshed", "Refreshed secret caused by HarborRobotBinding '%s', next refresh time: '%s'", harborRobotBinding.Name, harborRobotBinding.Status.NextRefreshTime)

		return existingSecret, nil
	}

	log.Debugw("nothing changed, skip updating image pull secret", "name", secret.Name)
	return existingSecret, nil
}

// MakeImagePullSecret creates a corev1.Secret with type dockerconfigjson from the given credentials
func MakeImagePullSecret(namespace string, harborRobotBinding *v1alpha1.HarborRobotBinding, robotName string, baseURL string, token string) corev1.Secret {
	auth := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", robotName, token)))
	configJSON := fmt.Sprintf(`{"auths":{"%s":{"username":"%s","password":"%s","auth":"%s"}}}`, baseURL, robotName, token, auth)

	name := harborRobotBinding.Name + ".robot"
	if harborRobotBinding.Spec.GeneratedSecret != nil && harborRobotBinding.Spec.GeneratedSecret.Name != "" {
		name = harborRobotBinding.Spec.GeneratedSecret.Name
	}

	return corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: namespace,
			Name:      name,
			Annotations: map[string]string{
				"goharbor.io/project": harborRobotBinding.Spec.Harbor.Project,
				"goharbor.io/robot":   robotName,
				lastRefreshTimeKey:    harborRobotBinding.Status.LastRefreshTime.Format(time.RFC3339),
				nextRefreshTimeKey:    harborRobotBinding.Status.NextRefreshTime.Format(time.RFC3339),
			},
			Labels: secretLabelsGenerated(harborRobotBinding),
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(harborRobotBinding, v1alpha1.GroupVersion.WithKind("HarborRobotBinding")),
			},
		},
		Type: corev1.SecretTypeDockerConfigJson,
		Data: map[string][]byte{
			corev1.DockerConfigJsonKey: []byte(configJSON),
		},
	}
}

// MakeRobotSecret creates a corev1.Secret with type kubernetes.io/opaque from the given credentials
func MakeRobotSecret(harborRobotBinding *v1alpha1.HarborRobotBinding, robotName string, baseURL string, token string) corev1.Secret {
	return corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: system.Namespace(),
			Name:      harborRobotBinding.Name + ".robot",
			Annotations: map[string]string{
				"goharbor.io/project": harborRobotBinding.Spec.Harbor.Project,
				"goharbor.io/robot":   robotName,
				lastRefreshTimeKey:    harborRobotBinding.Status.LastRefreshTime.Format(time.RFC3339),
				nextRefreshTimeKey:    harborRobotBinding.Status.NextRefreshTime.Format(time.RFC3339),
			},
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(harborRobotBinding, v1alpha1.GroupVersion.WithKind("HarborRobotBinding")),
			},
		},
		Type: corev1.SecretTypeOpaque,
		Data: map[string][]byte{
			"token": []byte(token),
		},
	}
}

func (r *HarborRobotBindingReconciler) updateServiceAccountInNamespace(ctx context.Context, namespace string, harborRobotBinding *v1alpha1.HarborRobotBinding, secret *corev1.Secret) error {
	log := logging.FromContext(ctx)

	if harborRobotBinding.Spec.ServiceAccount == nil {
		log.Debug("service account is nil, skip")
		return nil
	}

	// Get service account
	sa := &corev1.ServiceAccount{}
	err := r.Client.Get(ctx, types.NamespacedName{
		Namespace: namespace,
		Name:      harborRobotBinding.Spec.ServiceAccount.Name,
	}, sa)

	if err != nil {
		return fmt.Errorf("failed to get service account: %w", err)
	}

	// Check if the secret is already in the service account's imagePullSecrets
	for _, pullSecret := range sa.ImagePullSecrets {
		if pullSecret.Name == secret.Name {
			log.Debugw("image pull secret already exists in service account", "namespace", sa.Namespace, "name", sa.Name, "secret", secret.Name)
			return nil
		}
	}

	// Append the secret to imagePullSecrets
	sa.ImagePullSecrets = append(sa.ImagePullSecrets, corev1.LocalObjectReference{Name: secret.Name})

	// Update service account
	if err := r.Client.Update(ctx, sa); err != nil {
		return fmt.Errorf("failed to update service account: %w", err)
	}
	log.Infow("appended image pull secret to service account", "sa", sa.Namespace+"/"+sa.Name, "secret", secret.Name)

	return nil
}

// Name return HarborRobotBinding name
func (r *HarborRobotBindingReconciler) Name() string {
	return "harborrobotbinding-controller"
}

// CheckSetup will Check whether the prerequisites are met
func (r *HarborRobotBindingReconciler) CheckSetup(ctx context.Context, mgr ctrl.Manager, logger *zap.SugaredLogger) error {
	return nil
}

// Setup prepares and initializes the controller for usage
func (r *HarborRobotBindingReconciler) Setup(ctx context.Context, mgr ctrl.Manager, logger *zap.SugaredLogger) error {
	r.SugaredLogger = logger
	r.Client = mgr.GetClient()
	r.Scheme = mgr.GetScheme()
	r.Events = mgr.GetEventRecorderFor(r.Name())
	r.httpClient = kclient.NewHTTPClient(kclient.InsecureSkipVerifyOption)
	if r.NewHarborClient == nil {
		r.NewHarborClient = thirdpartyclient.NewHarborClient
	}

	return r.SetupWithManager(mgr)
}

// SetupWithManager sets up the controller with the Manager.
func (r *HarborRobotBindingReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(
			&v1alpha1.HarborRobotBinding{},
			builder.WithPredicates(predicate.Or[ctrlclient.Object](
				predicate.GenerationChangedPredicate{},
				predicate.AnnotationChangedPredicate{},
				predicate.LabelChangedPredicate{},
			)),
		).
		Watches(
			&corev1.Namespace{},
			handler.EnqueueRequestsFromMapFunc(r.GetHarborRobotBindingByNamespace),
			builder.WithPredicates(predicate.Or[ctrlclient.Object](
				pkgctrl.AnnotationChangedPredicate{},
				predicate.LabelChangedPredicate{},
			)),
		).
		WithOptions(pkgctrl.DefaultOptions()).
		Complete(r)
}

func (r *HarborRobotBindingReconciler) GetHarborRobotBindingByNamespace(ctx context.Context, obj ctrlclient.Object) []reconcile.Request {
	log := logging.FromContext(ctx)
	namespace := obj.(*corev1.Namespace)

	// Get all HarborRobotBindings
	bindings := &v1alpha1.HarborRobotBindingList{}
	err := r.Client.List(ctx, bindings)
	if err != nil {
		log.Errorw("failed to list harbor robot bindings", "error", err)
		return nil
	}

	// Find all bindings that match this namespace
	requests := []reconcile.Request{}
	for i := range bindings.Items {
		binding := &bindings.Items[i]

		// Check if this namespace matches the binding's namespace selector
		matches, err := matchNamespace(namespace, *binding)
		if err != nil {
			log.Errorw("error matching namespace", "namespace", namespace.Name, "binding", binding.Name, "error", err)
			continue
		}

		if matches {
			log.Infow("namespace matches binding", "namespace", namespace.Name, "binding", binding.Name)
			requests = append(requests, reconcile.Request{
				NamespacedName: types.NamespacedName{
					Name: binding.Name,
				},
			})
		}
	}

	return requests
}

func matchNamespace(ns *corev1.Namespace, binding v1alpha1.HarborRobotBinding) (bool, error) {
	// Check if namespace is in the explicit names list
	if len(binding.Spec.Namespaces.Names) > 0 {
		for _, name := range binding.Spec.Namespaces.Names {
			if name == ns.Name {
				return true, nil
			}
		}
	}

	// Check if namespace matches the label selector
	if binding.Spec.Namespaces.Selector != nil {
		selector, err := metav1.LabelSelectorAsSelector(binding.Spec.Namespaces.Selector)
		if err != nil {
			return false, fmt.Errorf("invalid label selector: %w", err)
		}

		// Check if the namespace's labels match the selector
		if selector.Matches(labels.Set(ns.Labels)) {
			return true, nil
		}
	}

	return false, nil
}
