/*
Copyright 2025 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controllers

import (
	"context"
	"encoding/json"
	"time"

	v1alpha1 "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/apis/v1alpha1"
	ktesting "github.com/AlaudaDevops/pkg/testing"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"go.uber.org/zap"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/client-go/tools/record"
	"knative.dev/pkg/logging"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

var _ = Describe("AddressAliasReconciler.ensureAliasImagePullSecret", func() {
	var (
		ctx                    context.Context
		reconciler             *AddressAliasReconciler
		fakeClient             ctrlclient.Client
		imagePullSecret        *corev1.Secret
		harborConnectionConfig *v1alpha1.HarborConnectionConfig
		harborRobotBinding     *v1alpha1.HarborRobotBinding
		serviceAccount         *corev1.ServiceAccount
		aliasCheckInterval     time.Duration
	)

	BeforeEach(func() {
		ctx = context.Background()
		ctx = logging.WithLogger(ctx, zap.NewNop().Sugar())
		aliasCheckInterval = 2 * time.Minute

		// Load test data from YAML files
		imagePullSecret = &corev1.Secret{}
		Expect(ktesting.LoadYAML("testdata/address_alias.image_pull_secret.yaml", imagePullSecret)).To(Succeed())

		harborRobotBinding = &v1alpha1.HarborRobotBinding{}
		Expect(ktesting.LoadYAML("testdata/address_alias.harbor_robot_binding.yaml", harborRobotBinding)).To(Succeed())

		serviceAccount = &corev1.ServiceAccount{}
		Expect(ktesting.LoadYAML("testdata/address_alias.service_account.yaml", serviceAccount)).To(Succeed())

		// Setup HarborConnectionConfig
		harborConnectionConfig = &v1alpha1.HarborConnectionConfig{
			Url:                "https://harbor.example.com",
			Username:           "admin",
			Password:           "admin",
			AddressAlias:       "https://harbor-alias.example.com",
			AliasPolicy:        v1alpha1.HarborAddressAliasPolicyAlways,
			AliasCheckInterval: &aliasCheckInterval,
		}
	})

	JustBeforeEach(func() {
		// Create reconciler
		reconciler = &AddressAliasReconciler{
			Client:        fakeClient,
			SugaredLogger: log,
			Scheme:        scheme,
			Events:        &record.FakeRecorder{},
		}
	})

	When("creating alias secret and updating ServiceAccount", func() {
		BeforeEach(func() {
			// Create fake client with pre-existing objects
			fakeClient = fake.NewClientBuilder().
				WithScheme(scheme).
				WithObjects(imagePullSecret, harborRobotBinding, serviceAccount).
				Build()
		})

		It("should successfully create alias secret and add it to ServiceAccount ImagePullSecrets", func() {
			// Execute method
			err := reconciler.ensureAliasImagePullSecret(ctx, imagePullSecret, harborConnectionConfig)
			Expect(err).To(BeNil())

			// Verify alias secret is created
			aliasSecret := &corev1.Secret{}
			aliasSecretName := buildAliasSecretName(imagePullSecret.Name)
			err = fakeClient.Get(ctx, ctrlclient.ObjectKey{
				Namespace: imagePullSecret.Namespace,
				Name:      aliasSecretName,
			}, aliasSecret)
			Expect(err).To(BeNil())

			// Verify alias secret docker config content
			var aliasDockerConfig map[string]interface{}
			err = json.Unmarshal(aliasSecret.Data[corev1.DockerConfigJsonKey], &aliasDockerConfig)
			Expect(err).To(BeNil())

			auths, ok := aliasDockerConfig["auths"].(map[string]interface{})
			Expect(ok).To(BeTrue())
			Expect(auths).To(HaveKey("https://harbor-alias.example.com"))
			Expect(auths).NotTo(HaveKey("https://harbor.example.com"))

			// Verify authentication info is correct
			aliasAuth, ok := auths["https://harbor-alias.example.com"].(map[string]interface{})
			Expect(ok).To(BeTrue())
			Expect(aliasAuth["username"]).To(Equal("robot$test+user"))
			Expect(aliasAuth["password"]).To(Equal("test-token"))
			Expect(aliasAuth["auth"]).To(Equal("cm9ib3QkdGVzdCt1c2VyOnRlc3QtdG9rZW4="))

			// Verify ServiceAccount is updated
			updatedSA := &corev1.ServiceAccount{}
			err = fakeClient.Get(ctx, ctrlclient.ObjectKey{
				Namespace: serviceAccount.Namespace,
				Name:      serviceAccount.Name,
			}, updatedSA)
			Expect(err).To(BeNil())
			Expect(updatedSA.ImagePullSecrets).To(HaveLen(1))
			Expect(updatedSA.ImagePullSecrets[0].Name).To(Equal(aliasSecretName))

			// Verify alias secret OwnerReference
			Expect(aliasSecret.OwnerReferences).To(HaveLen(1))
			Expect(aliasSecret.OwnerReferences[0].Name).To(Equal(imagePullSecret.Name))
			Expect(aliasSecret.OwnerReferences[0].Kind).To(Equal("Secret"))
		})
	})

	When("updating existing alias secret and ServiceAccount", func() {
		var existingAliasSecret *corev1.Secret

		BeforeEach(func() {
			// Load existing alias secret from YAML
			existingAliasSecret = &corev1.Secret{}
			Expect(ktesting.LoadYAML("testdata/address_alias.existing_alias_secret.yaml", existingAliasSecret)).To(Succeed())

			// Update ServiceAccount to include existing alias secret
			serviceAccount.ImagePullSecrets = []corev1.LocalObjectReference{
				{Name: buildAliasSecretName(imagePullSecret.Name)},
			}

			// Create fake client with all objects including existing alias secret
			fakeClient = fake.NewClientBuilder().
				WithScheme(scheme).
				WithObjects(imagePullSecret, harborRobotBinding, serviceAccount, existingAliasSecret).
				Build()
		})

		It("should successfully update existing alias secret content", func() {
			// Execute method
			err := reconciler.ensureAliasImagePullSecret(ctx, imagePullSecret, harborConnectionConfig)
			Expect(err).To(BeNil())

			// Verify alias secret is updated
			aliasSecret := &corev1.Secret{}
			aliasSecretName := buildAliasSecretName(imagePullSecret.Name)
			err = fakeClient.Get(ctx, ctrlclient.ObjectKey{
				Namespace: imagePullSecret.Namespace,
				Name:      aliasSecretName,
			}, aliasSecret)
			Expect(err).To(BeNil())

			// Verify alias secret content is updated
			var config map[string]interface{}
			err = json.Unmarshal(aliasSecret.Data[corev1.DockerConfigJsonKey], &config)
			Expect(err).To(BeNil())

			auths, ok := config["auths"].(map[string]interface{})
			Expect(ok).To(BeTrue())
			Expect(auths).To(HaveKey("https://harbor-alias.example.com"))

		})
	})
})

var _ = Describe("AddressAliasReconciler.ensureAllAliasImagePullSecretRemoved", func() {
	var (
		ctx                  context.Context
		reconciler           *AddressAliasReconciler
		fakeClient           ctrlclient.Client
		imagePullSecret      *corev1.Secret
		imagePullSecret2     *corev1.Secret
		existingAliasSecret  *corev1.Secret
		existingAliasSecret2 *corev1.Secret
		serviceAccount       *corev1.ServiceAccount
		imagePullSecrets     []*corev1.Secret
	)

	BeforeEach(func() {
		ctx = context.Background()
		ctx = logging.WithLogger(ctx, zap.NewNop().Sugar())

		// Load test data from YAML files
		imagePullSecret = &corev1.Secret{}
		ktesting.MustLoadYaml("testdata/address_alias.image_pull_secret.yaml", imagePullSecret)

		imagePullSecret2 = &corev1.Secret{}
		ktesting.MustLoadYaml("testdata/address_alias.second_image_pull_secret.yaml", imagePullSecret2)

		existingAliasSecret = &corev1.Secret{}
		ktesting.MustLoadYaml("testdata/address_alias.existing_alias_secret.yaml", existingAliasSecret)

		existingAliasSecret2 = &corev1.Secret{}
		ktesting.MustLoadYaml("testdata/address_alias.second_existing_alias_secret.yaml", existingAliasSecret2)

		serviceAccount = &corev1.ServiceAccount{}
		ktesting.MustLoadYaml("testdata/address_alias.image_pull_secret_with_alias.yaml", serviceAccount)

		// Prepare slice of image pull secrets
		imagePullSecrets = []*corev1.Secret{imagePullSecret, imagePullSecret2}
	})

	JustBeforeEach(func() {
		// Create reconciler
		reconciler = &AddressAliasReconciler{
			Client:        fakeClient,
			SugaredLogger: log,
			Scheme:        scheme,
			Events:        &record.FakeRecorder{},
		}
	})

	When("removing alias secrets that exist", func() {
		BeforeEach(func() {
			// Update ServiceAccount to include both alias secrets
			serviceAccount.ImagePullSecrets = []corev1.LocalObjectReference{
				{Name: imagePullSecret.Name},
				{Name: imagePullSecret2.Name},
				{Name: buildAliasSecretName(imagePullSecret2.Name)},
				{Name: "other-secret"},
			}

			// Create fake client with all objects including existing alias secrets
			fakeClient = fake.NewClientBuilder().
				WithScheme(scheme).
				WithObjects(
					imagePullSecret, imagePullSecret2,
					serviceAccount,
				).
				Build()
		})

		It("should successfully remove all alias secrets and update ServiceAccount", func() {
			// Execute method
			err := reconciler.ensureAllAliasImagePullSecretRemoved(ctx, imagePullSecrets)
			Expect(err).To(BeNil())

			// Verify first alias secret is deleted
			aliasSecret1 := &corev1.Secret{}
			err = fakeClient.Get(ctx, ctrlclient.ObjectKey{
				Namespace: imagePullSecret.Namespace,
				Name:      buildAliasSecretName(imagePullSecret.Name),
			}, aliasSecret1)
			Expect(errors.IsNotFound(err)).To(BeTrue())

			// Verify second alias secret is deleted
			aliasSecret2 := &corev1.Secret{}
			err = fakeClient.Get(ctx, ctrlclient.ObjectKey{
				Namespace: imagePullSecret2.Namespace,
				Name:      buildAliasSecretName(imagePullSecret2.Name),
			}, aliasSecret2)
			Expect(errors.IsNotFound(err)).To(BeTrue())

			// Verify ServiceAccount is updated to remove alias secrets
			updatedSA := &corev1.ServiceAccount{}
			err = fakeClient.Get(ctx, ctrlclient.ObjectKey{
				Namespace: serviceAccount.Namespace,
				Name:      serviceAccount.Name,
			}, updatedSA)
			Expect(err).To(BeNil())

			Expect(updatedSA.ImagePullSecrets).Should(ConsistOf(
				corev1.LocalObjectReference{Name: "other-secret"},
				corev1.LocalObjectReference{Name: imagePullSecret.Name},
				corev1.LocalObjectReference{Name: imagePullSecret2.Name},
			))
		})
	})

	When("removing sa image pull secrets where alias image pull secrets don't exist", func() {
		BeforeEach(func() {
			// Only create one alias secret, not the second one
			serviceAccount.ImagePullSecrets = []corev1.LocalObjectReference{
				{Name: imagePullSecret.Name},
				{Name: buildAliasSecretName(imagePullSecret.Name)},
				{Name: "other-secret"},
			}

			fakeClient = fake.NewClientBuilder().
				WithScheme(scheme).
				WithObjects(
					imagePullSecret,
					serviceAccount,
				).
				Build()
		})

		It("should successfully remove existing alias secret in sa", func() {
			// Execute method
			err := reconciler.ensureAllAliasImagePullSecretRemoved(ctx, imagePullSecrets)
			Expect(err).To(BeNil())

			// Verify ServiceAccount is updated to remove the existing alias secret
			updatedSA := &corev1.ServiceAccount{}
			err = fakeClient.Get(ctx, ctrlclient.ObjectKey{
				Namespace: serviceAccount.Namespace,
				Name:      serviceAccount.Name,
			}, updatedSA)
			Expect(err).To(BeNil())

			// Should remove the alias image pull secret in sa
			Expect(updatedSA.ImagePullSecrets).Should(ConsistOf(corev1.LocalObjectReference{Name: "other-secret"}, corev1.LocalObjectReference{Name: imagePullSecret.Name}))
		})
	})

	When("removing alias secrets with empty input", func() {
		BeforeEach(func() {
			// Create fake client with existing alias secrets
			fakeClient = fake.NewClientBuilder().
				WithScheme(scheme).
				WithObjects(existingAliasSecret, existingAliasSecret2, serviceAccount).
				Build()

			// Pass empty slice
			imagePullSecrets = []*corev1.Secret{}
		})

		It("should handle empty input gracefully", func() {
			// Execute method
			err := reconciler.ensureAllAliasImagePullSecretRemoved(ctx, imagePullSecrets)
			Expect(err).To(BeNil())

			// Verify existing alias secrets are not affected
			aliasSecret1 := &corev1.Secret{}
			err = fakeClient.Get(ctx, ctrlclient.ObjectKey{
				Namespace: existingAliasSecret.Namespace,
				Name:      existingAliasSecret.Name,
			}, aliasSecret1)
			Expect(err).To(BeNil())

			aliasSecret2 := &corev1.Secret{}
			err = fakeClient.Get(ctx, ctrlclient.ObjectKey{
				Namespace: existingAliasSecret2.Namespace,
				Name:      existingAliasSecret2.Name,
			}, aliasSecret2)
			Expect(err).To(BeNil())
		})
	})
})
