/*
Copyright 2025 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controllers

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	goerrors "errors"

	v1alpha1 "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/apis/v1alpha1"
	pkgnet "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/net"

	secretpkg "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/secret"
	pkgctrl "github.com/AlaudaDevops/pkg/controllers"
	"go.uber.org/zap"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	"knative.dev/pkg/logging"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
)

// AddressAliasReconciler reconciles
type AddressAliasReconciler struct {
	ctrlclient.Client

	SugaredLogger *zap.SugaredLogger
	Scheme        *runtime.Scheme
	Events        record.EventRecorder
}

// Reconcile is the main function of the address alias controller
// it will reconcile to ensure alias secret is as expected by the harbor secret or image pull secret
// when harbor secret changes alias configuration, we should reconcile to ensure all image pull secrets have correct alias secret, create or update or delete the alias secret
// when image pull secret's data refreshed, we should update the data of alias secret same as the image pull secret
func (r *AddressAliasReconciler) Reconcile(ctx context.Context, req ctrl.Request) (result ctrl.Result, err error) {
	log := logging.FromContext(ctx).With("key", req.NamespacedName)
	ctx = logging.WithLogger(ctx, log)

	log.Infow("reconciling address alias")

	now := time.Now()
	defer func() {
		log.Infow("reconciled address alias", "error", err, "elapsed", time.Since(now))
	}()

	secret := &corev1.Secret{}
	err = r.Client.Get(ctx, req.NamespacedName, secret)
	if err != nil && !errors.IsNotFound(err) {
		return ctrl.Result{}, err
	}

	if errors.IsNotFound(err) {
		log.Infow("secret not found, removing alias image pull secret in service account", "secret", req.Namespace+"/"+req.Name)
		err = r.removeImagePullSecretInServiceAccount(ctx, req.Namespace, buildAliasSecretName(req.Name))

		return ctrl.Result{}, err
	}

	if secret.GetLabels() == nil {
		return ctrl.Result{}, nil
	}

	// when harbor secret changes alias, we should reconcile to ensure all image pull secrets have alias secret
	if secret.GetLabels()[v1alpha1.HarborRobotGenSecretLabel] == v1alpha1.HarborRobotGenSecretLabelValue {
		log.Infow("caused by harbor secret", "secret", secret.Namespace+"/"+secret.Name)
		return r.reconcileByHarborSecret(ctx, secret)
	}

	// when imagepullsecret's data refreshed, we should update the alias secret
	if secret.GetLabels()[LabelGeneratedBy] == ValueGeneratedByHarborRobotBinding {
		log.Infow("caused by image pull secret", "secret", secret.Namespace+"/"+secret.Name)
		return r.reconcileByImagePullSecret(ctx, secret)
	}

	return ctrl.Result{}, err
}

func (r *AddressAliasReconciler) reconcileByImagePullSecret(ctx context.Context, imagePullSecret *corev1.Secret) (result ctrl.Result, err error) {
	log := logging.FromContext(ctx)

	aliasSecret := &corev1.Secret{}
	err = r.Client.Get(ctx, types.NamespacedName{Namespace: imagePullSecret.Namespace, Name: buildAliasSecretName(imagePullSecret.Name)}, aliasSecret)
	if err != nil {
		if errors.IsNotFound(err) {
			return ctrl.Result{}, nil
		}

		return ctrl.Result{Requeue: true}, err
	}

	addresses, err := secretpkg.GetRegistryAddressFromDockerConfigJson(imagePullSecret)
	if err != nil {
		log.Errorw("failed to get registry address from image pull secret", "error", err, "secret", imagePullSecret.Namespace+"/"+imagePullSecret.Name)
		return ctrl.Result{}, err
	}
	address := addresses[0]

	aliasAddresses, err := secretpkg.GetRegistryAddressFromDockerConfigJson(aliasSecret)
	if err != nil {
		log.Errorw("failed to get registry address from alias secret", "error", err, "secret", aliasSecret.Namespace+"/"+aliasSecret.Name)
		return ctrl.Result{}, err
	}
	aliasAddress := aliasAddresses[0]

	expectedAliasSecret, err := makeAliasSecret(imagePullSecret, address, aliasAddress)
	if err != nil {
		log.Errorw("failed to make alias secret", "error", err, "secret", imagePullSecret.Namespace+"/"+imagePullSecret.Name, "address", address, "aliasAddress", aliasAddress)
		return ctrl.Result{}, err
	}

	// Update if data is different
	if !reflect.DeepEqual(aliasSecret.Data, expectedAliasSecret.Data) {
		aliasSecret.Data = expectedAliasSecret.Data
		err = r.Client.Update(ctx, aliasSecret)

		if err != nil {
			log.Errorw("failed to update alias secret", "error", err)
			return ctrl.Result{}, err
		}

		log.Infow("updated alias secret same as image pull secret", "aliasSecret", aliasSecret.Namespace+"/"+aliasSecret.Name, "imagePullSecret", imagePullSecret.Namespace+"/"+imagePullSecret.Name)
		return ctrl.Result{}, nil
	}

	log.Debugw("nothing changed, skip updating alias secret")
	return ctrl.Result{}, nil
}

// reconcileByHarborSecret will reconcile the alias secret by the harbor secret, the harbor secret saves the harbor connection config
//
// if harbor connection config has address alias, we should find all image pull secrets have the same registry address,
// and create or update the alias secret that has the same data as the image pull secret, but with the alias registry address
// and update the service account in the namespace of the image pull secret to use the alias secret
//
// if harbor connection config has no address alias, we should find all image pull secrets have the same registry address,
// and remove the alias secret if it exists
func (r *AddressAliasReconciler) reconcileByHarborSecret(ctx context.Context, harborSecret *corev1.Secret) (result ctrl.Result, err error) {
	log := logging.FromContext(ctx)

	harborConnectionConfig := &v1alpha1.HarborConnectionConfig{}
	err = harborConnectionConfig.FromSecret(harborSecret)
	if err != nil {
		log.Errorw("failed to get harbor connection config", "error", err)
		return ctrl.Result{}, err
	}

	defer func() {
		if harborConnectionConfig.AddressAlias != "" && err == nil {
			result.RequeueAfter = *harborConnectionConfig.AliasCheckInterval
			log.Infow("address alias reconciler will requeue after interval", "interval", harborConnectionConfig.AliasCheckInterval.String())
		}
	}()

	// list all image pull secret with same registry
	imagePullSecrets, err := r.listImagePullSecretHasRegistry(ctx, harborConnectionConfig.Url)
	if err != nil {
		return result, err
	}

	aliasPolicyMatched, err := r.aliasPolicyMatched(ctx, harborConnectionConfig)
	if err != nil {
		return result, err
	}

	if aliasPolicyMatched {
		log.Infow("alias policy matched, ensure all image pull secret has a correct alias secret", "policy", harborConnectionConfig.AliasPolicy, "address", harborConnectionConfig.Url, "alias", harborConnectionConfig.AddressAlias)

		err := r.ensureAllImagePullSecretHasAliasSecret(ctx, imagePullSecrets, harborConnectionConfig)
		if err != nil {
			return result, err
		}

		return result, nil
	}

	log.Infow("alias policy not matched, ensure all image pull secret has no alias secret", "address", harborConnectionConfig.Url, "alias", harborConnectionConfig.AddressAlias)
	err = r.ensureAllAliasImagePullSecretRemoved(ctx, imagePullSecrets)
	if err != nil {
		return result, err
	}

	return result, nil
}

func (r *AddressAliasReconciler) ensureAllImagePullSecretHasAliasSecret(ctx context.Context, imagePullSecrets []*corev1.Secret, harborConnectionConfig *v1alpha1.HarborConnectionConfig) error {
	errs := make([]error, 0, len(imagePullSecrets))

	for _, imagePullSecret := range imagePullSecrets {
		err := r.ensureAliasImagePullSecret(ctx, imagePullSecret, harborConnectionConfig)
		errs = append(errs, err)
	}

	if len(errs) == 0 {
		return nil
	}

	return goerrors.Join(errs...)
}

func (r *AddressAliasReconciler) ensureAllAliasImagePullSecretRemoved(ctx context.Context, imagePullSecrets []*corev1.Secret) error {
	errs := make([]error, 0, len(imagePullSecrets))

	for _, imagePullSecret := range imagePullSecrets {
		err := r.deleteAddressAliasSecret(ctx, imagePullSecret)
		errs = append(errs, err)
	}

	if len(errs) == 0 {
		return nil
	}

	return goerrors.Join(errs...)
}

func (r *AddressAliasReconciler) listImagePullSecretHasRegistry(ctx context.Context, registry string) ([]*corev1.Secret, error) {

	secretList := &corev1.SecretList{}
	err := r.Client.List(ctx, secretList, ctrlclient.MatchingLabels{LabelGeneratedBy: ValueGeneratedByHarborRobotBinding})
	if err != nil {
		return nil, err
	}

	secrets := make([]*corev1.Secret, 0, len(secretList.Items))

	for _, item := range secretList.Items {
		imagePullSecret := item

		addresses, err := secretpkg.GetRegistryAddressFromDockerConfigJson(&imagePullSecret)
		if err != nil {
			return nil, err
		}

		for _, address := range addresses {
			if address == registry {
				secrets = append(secrets, &imagePullSecret)
				break
			}
		}
	}

	return secrets, nil
}

func (r *AddressAliasReconciler) aliasPolicyMatched(ctx context.Context, harborConnectionConfig *v1alpha1.HarborConnectionConfig) (bool, error) {
	log := logging.FromContext(ctx)

	registryAddress := harborConnectionConfig.Url
	addressAlias := harborConnectionConfig.AddressAlias
	if addressAlias == "" {
		return false, nil
	}

	policy := harborConnectionConfig.AliasPolicy

	if policy == v1alpha1.HarborAddressAliasPolicyAlways {
		return true, nil
	}

	if policy == v1alpha1.HarborAddressAliasPolicyIfIPEqual {
		equal, err := pkgnet.IsIPResolvedEqual(ctx, registryAddress, addressAlias, false)
		if err != nil {
			log.Errorw("failed to check IP resolved equal", "error", err, "registryAddress", registryAddress, "addressAlias", addressAlias)
			return false, err
		}

		return equal, nil
	}

	return false, nil
}

func (r *AddressAliasReconciler) ensureAliasImagePullSecret(ctx context.Context, imagePullSecret *corev1.Secret, harborConnectionConfig *v1alpha1.HarborConnectionConfig) error {
	log := logging.FromContext(ctx)

	address := harborConnectionConfig.Url
	addressAlias := harborConnectionConfig.AddressAlias

	aliasImagePullSecret, err := r.upsertAliasImagePullSecret(ctx, imagePullSecret, address, addressAlias)
	if err != nil {
		return err
	}

	robotBindingName := imagePullSecret.Labels[HarborRobotBindingLabelKey]
	if robotBindingName == "" {
		log.Debugw("no harborrobotbinding in labels, skip updating service account", "secret", imagePullSecret.Name)
		return nil
	}

	robotBinding := &v1alpha1.HarborRobotBinding{}
	err = r.Client.Get(ctx, types.NamespacedName{Name: robotBindingName}, robotBinding)
	if err != nil {
		return err
	}

	if robotBinding.DeletionTimestamp != nil || robotBinding.Spec.ServiceAccount == nil || robotBinding.Spec.ServiceAccount.Name == "" {
		return nil
	}

	err = r.updateServiceAccountInNamespace(ctx, aliasImagePullSecret.Namespace, robotBinding.Spec.ServiceAccount.Name, aliasImagePullSecret.Name)
	if err != nil {
		return err
	}

	return nil
}

func (r *AddressAliasReconciler) updateServiceAccountInNamespace(ctx context.Context, namespace string, saName string, imagePullSecretName string) error {
	log := logging.FromContext(ctx)

	// Get service account
	sa := &corev1.ServiceAccount{}
	err := r.Client.Get(ctx, types.NamespacedName{
		Namespace: namespace,
		Name:      saName,
	}, sa)

	if err != nil {
		return fmt.Errorf("failed to get service account: %w", err)
	}

	// Check if the secret is already in the service account's imagePullSecrets
	for _, pullSecret := range sa.ImagePullSecrets {
		if pullSecret.Name == imagePullSecretName {
			log.Debugw("image pull secret already exists in service account", "namespace", sa.Namespace, "name", sa.Name, "secret", imagePullSecretName)
			return nil
		}
	}

	// Append the secret to imagePullSecrets
	sa.ImagePullSecrets = append(sa.ImagePullSecrets, corev1.LocalObjectReference{Name: imagePullSecretName})

	// Update service account
	if err := r.Client.Update(ctx, sa); err != nil {
		log.Errorw("failed to update service account", "error", err, "sa", sa.Namespace+"/"+sa.Name, "secret", imagePullSecretName)
		return err
	}

	log.Infow("appended image pull secret to service account", "sa", sa.Namespace+"/"+sa.Name, "secret", imagePullSecretName)

	return nil
}

// upsertAliasImagePullSecret will create or update the alias secret. The content is the same as the original secret, only the registry address is replaced by the alias
func (r *AddressAliasReconciler) upsertAliasImagePullSecret(ctx context.Context, originImagePullSecret *corev1.Secret, address, addressAlias string) (*corev1.Secret, error) {
	log := logging.FromContext(ctx)

	aliasSecret, err := makeAliasSecret(originImagePullSecret, address, addressAlias)
	if err != nil {
		return nil, err
	}
	log = log.With("aliasSecret", aliasSecret.Namespace+"/"+aliasSecret.Name)

	aliasSecretAlready := &corev1.Secret{}
	err = r.Client.Get(ctx, types.NamespacedName{Namespace: originImagePullSecret.Namespace, Name: aliasSecret.Name}, aliasSecretAlready)

	if err == nil {
		// Update if data changed
		if !reflect.DeepEqual(aliasSecretAlready.Data, aliasSecret.Data) || !reflect.DeepEqual(aliasSecretAlready.OwnerReferences, aliasSecret.OwnerReferences) {
			aliasSecretAlready.Data = aliasSecret.Data
			aliasSecretAlready.OwnerReferences = aliasSecret.OwnerReferences
			err = r.Client.Update(ctx, aliasSecretAlready)

			if err != nil {
				log.Errorw("failed to update alias secret", "error", err)
				return nil, err
			}

			log.Infow("updated alias secret", "name", aliasSecret.Name)
			return aliasSecretAlready, nil
		}

		log.Debugw("nothing changed, skip updating alias secret")
		return aliasSecretAlready, nil
	}

	if errors.IsNotFound(err) {

		err = r.Client.Create(ctx, aliasSecret)
		if err != nil {
			log.Errorw("failed to create alias secret", "error", err)
			return nil, err
		}

		log.Infow("created alias secret", "secret", aliasSecret.Namespace+"/"+aliasSecret.Name)
		return aliasSecret, nil
	}

	return nil, err
}

func makeAliasSecret(imagePullSecret *corev1.Secret, address, addressAlias string) (*corev1.Secret, error) {
	dockerConfig, ok := imagePullSecret.Data[corev1.DockerConfigJsonKey]
	if !ok {
		return nil, fmt.Errorf(".dockerconfigjson not found in secret %s", imagePullSecret.Name)
	}

	var configObj map[string]interface{}
	if err := json.Unmarshal(dockerConfig, &configObj); err != nil {
		return nil, err
	}
	auths, ok := configObj["auths"].(map[string]interface{})
	if !ok || len(auths) == 0 {
		return nil, nil
	}

	var newAuths = map[string]interface{}{}
	for key, value := range auths {
		if key == address {
			newAuths[addressAlias] = value
		} else {
			newAuths[key] = value
		}
	}

	configObj["auths"] = newAuths
	newDockerConfigJson, err := json.Marshal(configObj)
	if err != nil {
		return nil, err
	}

	return &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: imagePullSecret.Namespace,
			Name:      buildAliasSecretName(imagePullSecret.Name),
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(imagePullSecret, corev1.SchemeGroupVersion.WithKind("Secret")),
			},
		},
		Type: corev1.SecretTypeDockerConfigJson,
		Data: map[string][]byte{
			corev1.DockerConfigJsonKey: newDockerConfigJson,
		},
	}, nil
}

func buildAliasSecretName(imagePullSecretName string) string {
	return imagePullSecretName + ".alias"
}

func (r *AddressAliasReconciler) deleteAddressAliasSecret(ctx context.Context, imagePullSecret *corev1.Secret) error {
	log := logging.FromContext(ctx)

	aliasSecret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: imagePullSecret.Namespace,
			Name:      buildAliasSecretName(imagePullSecret.Name),
		},
	}
	log = log.With("aliasSecret", aliasSecret.Namespace+"/"+aliasSecret.Name)

	// error is nil, we should remove the image pull secret in service account and delete the alias secret
	err := r.removeImagePullSecretInServiceAccount(ctx, imagePullSecret.Namespace, aliasSecret.Name)
	if err != nil {
		log.Errorw("failed to remove image pull secret in service account", "error", err)
		return err
	}

	err = r.Client.Get(ctx, types.NamespacedName{Namespace: imagePullSecret.Namespace, Name: aliasSecret.Name}, aliasSecret)
	if errors.IsNotFound(err) {
		return nil
	}

	if err != nil {
		log.Errorw("failed to get alias secret", "error", err)
		return err
	}

	err = r.Client.Delete(ctx, aliasSecret)
	if err != nil {
		log.Errorw("failed to delete alias secret", "error", err)
		return err
	}

	log.Infow("deleted alias secret", "secret", aliasSecret.Namespace+"/"+aliasSecret.Name)

	return nil
}

func (r *AddressAliasReconciler) removeImagePullSecretInServiceAccount(ctx context.Context, namespace, secretName string) error {
	log := logging.FromContext(ctx)

	saList := &corev1.ServiceAccountList{}
	err := r.Client.List(ctx, saList, ctrlclient.InNamespace(namespace))
	if err != nil {
		return err
	}

	if len(saList.Items) == 0 {
		return nil
	}

	for _, item := range saList.Items {
		sa := item
		removed := secretpkg.RemoveImagePullSecretFromServiceAccount(&sa, secretName)

		if !removed {
			continue
		}

		err = r.Client.Update(ctx, &sa)
		if err != nil {
			log.Errorw("failed to update service account", "error", err, "serviceAccount", sa.Name)
			return err
		}

		log.Infow("removed image pull secret in service account", "serviceAccount", sa.Name, "secret", secretName)
	}

	return nil
}

// CheckSetup will Check whether the prerequisites are met
func (r *AddressAliasReconciler) CheckSetup(ctx context.Context, mgr ctrl.Manager, logger *zap.SugaredLogger) error {
	return nil
}

// Name return HarborRobotBinding name
func (r *AddressAliasReconciler) Name() string {
	return "address-alias-controller"
}

// Setup prepares and initializes the controller for usage
func (r *AddressAliasReconciler) Setup(ctx context.Context, mgr ctrl.Manager, logger *zap.SugaredLogger) error {
	r.SugaredLogger = logger
	r.Client = mgr.GetClient()
	r.Scheme = mgr.GetScheme()
	r.Events = mgr.GetEventRecorderFor(r.Name())

	return r.SetupWithManager(mgr)
}

// SetupWithManager sets up the controller with the Manager.
func (r *AddressAliasReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		Named(r.Name()).
		WithOptions(pkgctrl.DefaultOptions()).
		For(&corev1.Secret{}, builder.WithPredicates(predicate.Funcs{
			CreateFunc: func(e event.CreateEvent) bool {

				if e.Object.GetLabels() != nil {
					harborSecret := e.Object.GetLabels()[harborRobotGenSecretLabel] == "true"
					imagePullSecret := e.Object.GetLabels()[LabelGeneratedBy] == ValueGeneratedByHarborRobotBinding
					return harborSecret || imagePullSecret
				}
				return false
			},
			UpdateFunc: func(e event.UpdateEvent) bool {
				if e.ObjectNew.GetLabels() != nil {
					harborSecret := e.ObjectNew.GetLabels()[harborRobotGenSecretLabel] == "true"
					imagePullSecret := e.ObjectNew.GetLabels()[LabelGeneratedBy] == ValueGeneratedByHarborRobotBinding
					return harborSecret || imagePullSecret
				}
				return false
			},
			DeleteFunc: func(e event.DeleteEvent) bool {
				imagePullSecret := e.Object.GetLabels()[LabelGeneratedBy] == ValueGeneratedByHarborRobotBinding
				return imagePullSecret
			},
			GenericFunc: func(e event.GenericEvent) bool {
				return false
			},
		})).
		Complete(r)
}
