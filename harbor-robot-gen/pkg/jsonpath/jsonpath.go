/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Package jsonpath provide readablity and writeablity of golang struct by k8sjsonpath
package jsonpath

import (
	"k8s.io/client-go/util/jsonpath"
)

// Read will read value of data by jsonpath
func Read(data interface{}, path string) ([][]interface{}, error) {
	jpath := jsonpath.New("jsonpath")
	jpath.AllowMissingKeys(true)
	err := jpath.Parse(path)
	if err != nil {
		return nil, err
	}
	fullResults, err := jpath.FindResults(data)
	if err != nil {
		return nil, err
	}

	var readRes = [][]interface{}{}
	for i := range fullResults {
		items := fullResults[i]
		readResItems := []interface{}{}
		for j := range items {
			resultValue := items[j]
			readResItems = append(readResItems, resultValue.Interface())
		}
		readRes = append(readRes, readResItems)
	}

	return readRes, nil
}
