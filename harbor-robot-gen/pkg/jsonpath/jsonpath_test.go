/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package jsonpath

import (
	"reflect"
	"testing"
)

type book struct {
	Name     string        `json:"name"`
	Price    int           `json:"price"`
	Sections []bookSection `json:"sections"`
}

type bookSection struct {
	Name  string      `json:"name"`
	Title string      `json:"title"`
	Info  sectionInfo `json:"info"`
}

type sectionInfo struct {
	Page string `json:"page"`
}

var data book = book{
	Name:  "katanomi",
	Price: 100,
	Sections: []bookSection{
		{
			Name:  "pkg",
			Title: "pkg description",
			Info: sectionInfo{
				Page: "100",
			},
		},
		{
			Name:  "core",
			Title: "core description",
		},
		{
			Name:  "builds",
			Title: "builds description",
		},
	},
}

func TestRead(t *testing.T) {
	type caseData struct {
		Description   string
		Path          string
		Expected      interface{}
		ExpectedError bool
	}

	var items = []caseData{
		{"error field path", "{.name[]}", "", true},
		{"field path of struct, type is string", "{.name}", [][]interface{}{{"katanomi"}}, false},
		{"field path of struct, type is int", "{.price}", [][]interface{}{{int(100)}}, false},
		{"index filter in array object", "{.sections[1].name}", [][]interface{}{{"core"}}, false},
		{"attribute filter in array object", "{.sections[?(@.name=='pkg')].title}", [][]interface{}{{"pkg description"}}, false},
		{"attribute filter in array object and return array", "{.sections[?(@.name!='pkg')].title}", [][]interface{}{{"core description", "builds description"}}, false},
	}

	for _, item := range items {
		t.Run(item.Description, func(t *testing.T) {
			vals, err := Read(data, item.Path)
			if item.ExpectedError {
				if err == nil {
					t.Errorf("err should not nil")
					return
				}
				return
			}
			if !reflect.DeepEqual(vals, item.Expected) {
				t.Errorf("expected: %#v, but: %#v", item.Expected, vals)
			}
		})
	}
}
