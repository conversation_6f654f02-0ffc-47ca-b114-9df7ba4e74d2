/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package secret

import (
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

var _ = Describe("GetRegistryAddressFromDockerConfigJson", func() {
	var (
		secret *corev1.Secret
	)

	BeforeEach(func() {
		secret = &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-secret",
				Namespace: "test-namespace",
			},
			Type: corev1.SecretTypeDockerConfigJson,
			Data: map[string][]byte{},
		}
	})

	Context("valid docker config", func() {
		Context("single registry", func() {
			BeforeEach(func() {
				dockerConfig := `{
					"auths": {
						"https://harbor.example.com": {
							"username": "robot$test+user",
							"password": "test-token",
							"auth": "************************************"
						}
					}
				}`
				secret.Data[corev1.DockerConfigJsonKey] = []byte(dockerConfig)
			})

			It("should return single registry address", func() {
				addresses, err := GetRegistryAddressFromDockerConfigJson(secret)
				Expect(err).To(BeNil())
				Expect(addresses).To(HaveLen(1))
				Expect(addresses).To(ContainElement("https://harbor.example.com"))
			})
		})

		Context("multiple registries", func() {
			BeforeEach(func() {
				dockerConfig := `{
					"auths": {
						"https://harbor.example.com": {
							"username": "robot$test+user1",
							"password": "test-token1",
							"auth": "cm9ib3QkdGVzdCt1c2VyMTp0ZXN0LXRva2VuMQ=="
						},
						"https://registry.example.com": {
							"username": "robot$test+user2",
							"password": "test-token2",
							"auth": "cm9ib3QkdGVzdCt1c2VyMjp0ZXN0LXRva2VuMg=="
						},
						"https://docker.io": {
							"username": "testuser",
							"password": "testpass",
							"auth": "dGVzdHVzZXI6dGVzdHBhc3M="
						}
					}
				}`
				secret.Data[corev1.DockerConfigJsonKey] = []byte(dockerConfig)
			})

			It("should return all registry addresses", func() {
				addresses, err := GetRegistryAddressFromDockerConfigJson(secret)
				Expect(err).To(BeNil())
				Expect(addresses).To(HaveLen(3))
				Expect(addresses).To(ContainElements("https://harbor.example.com", "https://registry.example.com", "https://docker.io"))
			})
		})

		Context("empty auths", func() {
			BeforeEach(func() {
				dockerConfig := `{
					"auths": {}
				}`
				secret.Data[corev1.DockerConfigJsonKey] = []byte(dockerConfig)
			})

			It("should return empty slice", func() {
				addresses, err := GetRegistryAddressFromDockerConfigJson(secret)
				Expect(err).To(BeNil())
				Expect(addresses).To(BeEmpty())
			})
		})
	})
})

var _ = Describe("RemoveImagePullSecretFromServiceAccount", func() {
	var (
		serviceAccount *corev1.ServiceAccount
	)

	BeforeEach(func() {
		serviceAccount = &corev1.ServiceAccount{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-service-account",
				Namespace: "test-namespace",
			},
		}
	})

	Context("when service account has image pull secrets", func() {
		Context("when target secret exists in the list", func() {
			BeforeEach(func() {
				serviceAccount.ImagePullSecrets = []corev1.LocalObjectReference{
					{Name: "secret1"},
					{Name: "target-secret"},
					{Name: "secret2"},
				}
			})

			It("should remove the target secret and return true", func() {
				changed := RemoveImagePullSecretFromServiceAccount(serviceAccount, "target-secret")

				Expect(changed).To(BeTrue())
				Expect(serviceAccount.ImagePullSecrets).To(HaveLen(2))
				Expect(serviceAccount.ImagePullSecrets).To(ConsistOf(
					corev1.LocalObjectReference{Name: "secret1"},
					corev1.LocalObjectReference{Name: "secret2"},
				))
			})
		})

		Context("when target secret is the only one in the list", func() {
			BeforeEach(func() {
				serviceAccount.ImagePullSecrets = []corev1.LocalObjectReference{
					{Name: "target-secret"},
				}
			})

			It("should remove the secret and leave empty list", func() {
				changed := RemoveImagePullSecretFromServiceAccount(serviceAccount, "target-secret")

				Expect(changed).To(BeTrue())
				Expect(serviceAccount.ImagePullSecrets).To(BeEmpty())
			})
		})

		Context("when target secret does not exist in the list", func() {
			BeforeEach(func() {
				serviceAccount.ImagePullSecrets = []corev1.LocalObjectReference{
					{Name: "secret1"},
					{Name: "secret2"},
					{Name: "secret3"},
				}
			})

			It("should not modify the list and return false", func() {
				originalSecrets := make([]corev1.LocalObjectReference, len(serviceAccount.ImagePullSecrets))
				copy(originalSecrets, serviceAccount.ImagePullSecrets)

				changed := RemoveImagePullSecretFromServiceAccount(serviceAccount, "non-existent-secret")

				Expect(changed).To(BeFalse())
				Expect(serviceAccount.ImagePullSecrets).To(Equal(originalSecrets))
			})
		})
	})

	Context("when service account has no image pull secrets", func() {
		BeforeEach(func() {
			serviceAccount.ImagePullSecrets = []corev1.LocalObjectReference{}
		})

		It("should return false and not modify anything", func() {
			changed := RemoveImagePullSecretFromServiceAccount(serviceAccount, "any-secret")

			Expect(changed).To(BeFalse())
			Expect(serviceAccount.ImagePullSecrets).To(BeEmpty())
		})
	})

	Context("when service account has nil image pull secrets", func() {
		BeforeEach(func() {
			serviceAccount.ImagePullSecrets = nil
		})

		It("should return false and not modify anything", func() {
			changed := RemoveImagePullSecretFromServiceAccount(serviceAccount, "any-secret")

			Expect(changed).To(BeFalse())
			Expect(serviceAccount.ImagePullSecrets).To(BeEmpty())
		})
	})
})
