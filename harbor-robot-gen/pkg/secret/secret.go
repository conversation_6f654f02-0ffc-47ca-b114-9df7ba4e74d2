/*
Copyright 2025 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Package secret provides utilities for managing secrets.
package secret

import (
	"encoding/json"

	corev1 "k8s.io/api/core/v1"
)

// GetRegistryAddressFromDockerConfigJson parses the registry address from the .dockerconfigjson field in the Secret
func GetRegistryAddressFromDockerConfigJson(secret *corev1.Secret) ([]string, error) {
	dockerConfig, ok := secret.Data[corev1.DockerConfigJsonKey]
	if !ok {
		return []string{}, nil
	}
	var configObj map[string]interface{}
	if err := json.Unmarshal(dockerConfig, &configObj); err != nil {
		return []string{}, err
	}
	auths, ok := configObj["auths"].(map[string]interface{})
	if !ok || len(auths) == 0 {
		return []string{}, nil
	}

	addresses := make([]string, 0, len(auths))
	for addr := range auths {
		addresses = append(addresses, addr)
	}
	return addresses, nil
}

// RemoveImagePullSecretFromServiceAccount will remove the image pull secret from the service account
func RemoveImagePullSecretFromServiceAccount(sa *corev1.ServiceAccount, secretName string) (changed bool) {
	newSecrets := make([]corev1.LocalObjectReference, 0, len(sa.ImagePullSecrets))
	if sa.ImagePullSecrets == nil {
		return false
	}

	for _, pullSecret := range sa.ImagePullSecrets {
		if pullSecret.Name == secretName {
			changed = true
		} else {
			newSecrets = append(newSecrets, pullSecret)
		}
	}

	if !changed {
		return false
	}

	sa.ImagePullSecrets = newSecrets
	return changed
}
