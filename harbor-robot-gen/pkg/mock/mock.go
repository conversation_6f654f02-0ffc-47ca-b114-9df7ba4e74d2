/*
Copyright 2024 The AlaudaDevops Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Package mock provides mock for controller-runtime
package mock

//go:generate mockgen -package=mock -destination=./sigs.k8s.io/controller-runtime/pkg/manager/manager.go sigs.k8s.io/controller-runtime/pkg/manager Manager
//go:generate mockgen -package=mock -destination=./pkg/thirdpartyclient/interface.go github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/thirdpartyclient HarborClient
