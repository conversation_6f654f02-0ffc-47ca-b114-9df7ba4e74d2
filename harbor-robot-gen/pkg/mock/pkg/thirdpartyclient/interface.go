// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/thirdpartyclient (interfaces: HarborClient)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	thirdpartyclient "github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/thirdpartyclient"
	project "github.com/goharbor/go-client/pkg/sdk/v2.0/client/project"
	robot "github.com/goharbor/go-client/pkg/sdk/v2.0/client/robot"
	models "github.com/goharbor/go-client/pkg/sdk/v2.0/models"
	gomock "github.com/golang/mock/gomock"
)

// MockHarborClient is a mock of HarborClient interface.
type MockHarborClient struct {
	ctrl     *gomock.Controller
	recorder *MockHarborClientMockRecorder
}

// MockHarborClientMockRecorder is the mock recorder for MockHarborClient.
type MockHarborClientMockRecorder struct {
	mock *MockHarborClient
}

// NewMockHarborClient creates a new mock instance.
func NewMockHarborClient(ctrl *gomock.Controller) *MockHarborClient {
	mock := &MockHarborClient{ctrl: ctrl}
	mock.recorder = &MockHarborClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHarborClient) EXPECT() *MockHarborClientMockRecorder {
	return m.recorder
}

// CreateProject mocks base method.
func (m *MockHarborClient) CreateProject(arg0 context.Context, arg1 project.CreateProjectParams) (*models.Project, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateProject", arg0, arg1)
	ret0, _ := ret[0].(*models.Project)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateProject indicates an expected call of CreateProject.
func (mr *MockHarborClientMockRecorder) CreateProject(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateProject", reflect.TypeOf((*MockHarborClient)(nil).CreateProject), arg0, arg1)
}

// CreateRobotAccount mocks base method.
func (m *MockHarborClient) CreateRobotAccount(arg0 context.Context, arg1 models.RobotCreate) (*thirdpartyclient.HarborRobotBinding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRobotAccount", arg0, arg1)
	ret0, _ := ret[0].(*thirdpartyclient.HarborRobotBinding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRobotAccount indicates an expected call of CreateRobotAccount.
func (mr *MockHarborClientMockRecorder) CreateRobotAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRobotAccount", reflect.TypeOf((*MockHarborClient)(nil).CreateRobotAccount), arg0, arg1)
}

// DeleteRobotAccount mocks base method.
func (m *MockHarborClient) DeleteRobotAccount(arg0 context.Context, arg1, arg2 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRobotAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRobotAccount indicates an expected call of DeleteRobotAccount.
func (mr *MockHarborClientMockRecorder) DeleteRobotAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRobotAccount", reflect.TypeOf((*MockHarborClient)(nil).DeleteRobotAccount), arg0, arg1, arg2)
}

// GetBaseUrl mocks base method.
func (m *MockHarborClient) GetBaseUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBaseUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetBaseUrl indicates an expected call of GetBaseUrl.
func (mr *MockHarborClientMockRecorder) GetBaseUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBaseUrl", reflect.TypeOf((*MockHarborClient)(nil).GetBaseUrl))
}

// GetRobotAccounts mocks base method.
func (m *MockHarborClient) GetRobotAccounts(arg0 context.Context, arg1 int) ([]*thirdpartyclient.HarborRobotBinding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRobotAccounts", arg0, arg1)
	ret0, _ := ret[0].([]*thirdpartyclient.HarborRobotBinding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRobotAccounts indicates an expected call of GetRobotAccounts.
func (mr *MockHarborClientMockRecorder) GetRobotAccounts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRobotAccounts", reflect.TypeOf((*MockHarborClient)(nil).GetRobotAccounts), arg0, arg1)
}

// ListProjects mocks base method.
func (m *MockHarborClient) ListProjects(arg0 context.Context) ([]*models.Project, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListProjects", arg0)
	ret0, _ := ret[0].([]*models.Project)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListProjects indicates an expected call of ListProjects.
func (mr *MockHarborClientMockRecorder) ListProjects(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProjects", reflect.TypeOf((*MockHarborClient)(nil).ListProjects), arg0)
}

// RefreshRobotAccount mocks base method.
func (m *MockHarborClient) RefreshRobotAccount(arg0 context.Context, arg1, arg2 int, arg3 robot.RefreshSecParams) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshRobotAccount", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RefreshRobotAccount indicates an expected call of RefreshRobotAccount.
func (mr *MockHarborClientMockRecorder) RefreshRobotAccount(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshRobotAccount", reflect.TypeOf((*MockHarborClient)(nil).RefreshRobotAccount), arg0, arg1, arg2, arg3)
}
