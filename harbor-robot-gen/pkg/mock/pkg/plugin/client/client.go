// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/AlaudaDevops/experimental/harbor-robot-gen/pkg/plugin/client (interfaces: Interface)

// Package client is a generated GoMock package.
package client

import (
	context "context"
	url "net/url"
	reflect "reflect"

	v1alpha1 "github.com/AlaudaDevops/pkg/apis/meta/v1alpha1"
	gomock "github.com/golang/mock/gomock"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// GetResource mocks base method.
func (m *MockInterface) GetResource(arg0 context.Context, arg1 string, arg2 url.Values, arg3 *v1alpha1.ListOptions) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResource", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResource indicates an expected call of GetResource.
func (mr *MockInterfaceMockRecorder) GetResource(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResource", reflect.TypeOf((*MockInterface)(nil).GetResource), arg0, arg1, arg2, arg3)
}
